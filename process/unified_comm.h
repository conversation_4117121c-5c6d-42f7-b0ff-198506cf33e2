#ifndef COMMUNICATOR_H
#define COMMUNICATOR_H

#include <functional>
#include <iostream>
#include <thread>
#include <unistd.h>
#include <vector>

// RCS
#include "nmldefine.h"
#include "buffer_10kb.hh"
#include "buffer_1mb.hh"
#include "buffer_2mb.hh"
#include "buffer_500kb.hh"
#include RCS_BUF_TaskList
// XSCOM
#include <xscom/xscom.h>
// Proto
#include "base/local_pose.pb.h"
#include "channeldefine.h"
#include "common/common.h"
#include "hdmap/local_hdmap.pb.h"
#include "nodedefine.h"
#include "perception/perception_common.pb.h"
#include "perception/perception_object_info.pb.h"
#include "perception/traffic_light_info.pb.h"
#include "globalpath/task_list.pb.h"
// #include "trigger_param.h"
#include "params/params.hpp"

// 屏蔽头文件xsdaq.hpp的警告信息
#include "xsdaq_wrapper.hpp"

#define NODE_NAME "data_auto_trigger" //"TriggerCollect"
#define XS_UGV_COM_RCS "rcs"
#define XS_UGV_COM_XSCOM "xscom"

using namespace xsproto;
using namespace xsnetwork;
using namespace xsproto::common;
using LocalPoseMsg = xsproto::base::LocalPose;
using LocalHdmapMsg = xsproto::hdmap::LocalHDMap;
using TrafficLightMsg = xsproto::perception::TrafficLightInfo;
using PerceptionMsg = xsproto::perception::PerceptionObjectInfo;
using TaskListMsg = xsproto::globalpath::TaskList;

enum CommType : int { CommType_RCS, CommType_XSCOM };

// 定义读取函数指针类型
using ReadFunction = std::function<bool()>;

class UnifiedComm {
public:
    // constructor and destructor
    UnifiedComm();

    ~UnifiedComm();

public:
    // get data
    std::deque<PerceptionMsg> GetPerceptionQueue();

    // get hdmap data
    std::deque<LocalHdmapMsg> GetHDmapQueue();

    // get traffic light data
    std::deque<TrafficLightMsg> GetTrafficLightQueue();

    std::deque<TaskListMsg> GetTaskListQueue();

private:
    // init channel
    bool InitRcs();

    bool InitXscom();

    // 合并后的统一读取函数
    void ReadDataToQueue();

    // bool ReadLocalPose();
    //  rcs data read function
    bool RcsReadLocalHdmap();

    bool RcsReadTrafficLight();

    bool RcsReadFusedInfo();

    bool RcsReadTaskList();

    // xscom read function
    bool CrtReadLocalHdmap();

    bool CrtReadFusedInfo();

    bool CrtReadTrafficLight();

    bool CrtReadTaskList();

private:
    // assist function
    // get program path
    std::string GetProgramPath();

public:
    // results send function
    void SendTask(TriggerParam &param);

private:
    // std::deque<LocalPoseMsg> lp_deque_;
    std::deque<LocalHdmapMsg> hdmap_deque_;
    std::deque<TrafficLightMsg> trafficlight_deque_;
    std::deque<PerceptionMsg> perception_deque_;
    std::deque<TaskListMsg> tasklist_deque_;
    
    // data lock
    // std::mutex lp_mutex_;
    std::mutex hdmap_mutex_;
    std::mutex trafficlight_mutex_;
    std::mutex perception_mutex_;
    std::mutex tasklist_mutex_;

private:
    CommType ugv_com_{CommType_RCS};
    std::thread std_thread_;

    // 读取函数指针数组（根据通信类型初始化）
    std::vector<ReadFunction> read_functions_;

    // NML *lp_rcs_channel_{nullptr};
    NML *hdmap_rcs_channel_{nullptr};
    NML *tl_rcs_channel_{nullptr};
    NML *perception_rcs_channel_{nullptr};
    NML *tasklist_rcs_channel_{nullptr};

    // rcs cache
    // std::shared_ptr<LocalPoseMsg> rcs_lp_cache_{nullptr};
    std::shared_ptr<LocalHdmapMsg> rcs_hdmap_cache_{nullptr};
    std::shared_ptr<TrafficLightMsg> rcs_tl_cache_{nullptr};
    std::shared_ptr<PerceptionMsg> rcs_fused_cache_{nullptr};
    std::shared_ptr<TaskListMsg> rcs_tasklist_cache_{nullptr};

    // xscom
    std::unique_ptr<xscom::Node> crt_node_{nullptr};
    std::shared_ptr<xscom::Reader<PerceptionMsg>> crt_perception_reader_{nullptr};
    std::shared_ptr<xscom::Reader<LocalHdmapMsg>> crt_hdmap_reader_{nullptr};
    std::shared_ptr<xscom::Reader<TrafficLightMsg>> crt_trafficlight_reader_{nullptr};
    std::shared_ptr<xscom::Reader<TaskListMsg>> crt_tasklist_reader_{nullptr};

private:
    //static constexpr size_t kFrameNumPerSec = 10; // 10 frames per second
    static constexpr size_t kPerceptionMaxQueueSize = 10; // perception cache size: 1 second
    static constexpr size_t kTrafficLightMaxQueueSize = 10; // traffic light cache size: 1 second
    static constexpr size_t kHdmapMaxQueueSize = 3; // hdmap cache size: 3
    static constexpr size_t kTaskListMaxQueueSize = 3; // task list cache size: 1 second
};

#endif
