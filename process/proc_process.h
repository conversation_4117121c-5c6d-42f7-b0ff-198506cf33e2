#ifndef PROC_PROCESS_H_
#define PROC_PROCESS_H_

#include <atomic>
#include <memory>
#include <unistd.h>

#include <functional>
#include <iostream>
#include <thread>

#include "unified_comm.h"
#include "obstacle.hpp"

// RCS
#include "buffer_10kb.hh"
#include "buffer_2mb.hh"
// XSCOM
#include <xscom/xscom.h>

// XSProto
#include "base/global_pose.pb.h"
#include "base/local_pose.pb.h"
#include "channeldefine.h"
#include "common/common.h"
#include "nodedefine.h"

// 触发器参数
#include "trigger_param.h"

#include "hdmap_mgr.hpp"
#include "scene_mgr.hpp"
#include "task_mgr.hpp"
#include "tl_mgr.hpp"

#include "params/params.hpp"

class ProcProcess {
    // 触发器参数列表（存储所有启用的触发器）
    // TriggerParamList all_triggers_;

public:
    ProcProcess();

    ~ProcProcess();

public:
    void LoopProcess();

    // void Trigger(bool (TrafficLightTrigger::*func)(UnifiedComm *, TrafficCollectParam), TrafficLightTrigger *obj,
    //              TrafficCollectParam param);

    // void Trigger(bool (PerceptionDataTrigger::*func)(UnifiedComm *, PerceptionTriggerParam),
    //              PerceptionDataTrigger *obj, PerceptionTriggerParam param);

    // 可视化
    void Visualize(const std::unique_ptr<HdmapManager> &hdmap_mgr, const std::unique_ptr<SceneManager> &agent_mgr,
                   const std::unique_ptr<TrafficLightManager> &tl_mgr);

private:
    void ClearManagerCache();

    void DataCaptureWaitThread(int time_sec);

    bool TrafficLightEvent(const Header &header, const std::unique_ptr<TrafficLightManager> &tl_mgr,
                           const std::unique_ptr<TaskManager> &task_mgr, const std::unique_ptr<HdmapManager> &hdmap_mgr,
                           TriggerParam &trigger_param);

    // 通过前方车道线内运动目标的速度, 推导车道线的速度
    float CalcTrafficFlow(const std::unique_ptr<SceneManager> &scene_mgr, std::unique_ptr<HdmapManager> &hdmap_mgr);

    // 使用地图信息, 计算前方车道线转向信息, 并设置到task_mgr中
    void UpdateFrontTurnInfo(const std::unique_ptr<HdmapManager> &hdmap_mgr, std::unique_ptr<TaskManager> &task_mgr);

    // 检查红绿灯与交通流的一致性
    bool CheckTrafficLightConsistency(const std::unique_ptr<HdmapManager> &hdmap_mgr,
                                      const std::unique_ptr<TaskManager> &task_mgr,
                                      const std::unique_ptr<TrafficLightManager> &tl_mgr);

    // // 检查突然出现的障碍物
    // bool CheckSuddenObstacleEvent_V2(const std::unique_ptr<SceneManager> &scene_mgr, const int max_frame_num = 5);

    bool CheckSuddenObstacleEvent(const std::unique_ptr<SceneManager> &scene_mgr, const std::unique_ptr<HdmapManager> &hdmap_mgr, const int cmp_frame_off_num = 5, bool debug_viz = false);
    
    // // 可视化新检测到的障碍物
    // void VisualizeNewObstacles(const std::vector<NewObstacleRegion>& new_obstacles,
    //                           double ego_x, double ego_y,
    //                           const std::vector<std::vector<cv::Point2f>>& all_obstacles);

private:
    double current_timestamp_ = 0.0;
    Header header_{};
    std::unique_ptr<UnifiedComm> unified_comm_{nullptr};

    // 采集触发参数
    TriggerParam trigger_param_{};
    bool has_trigger_event_{false};

    // 触发检测控制机制
    std::atomic<bool> is_trigger_mode_{true}; // 是否在触发检测模式
    std::atomic<bool> is_thread_running_{false}; // 是否有数据采集线程在运行
    std::set<std::string> trigger_events_;

    // std::unique_ptr<PerceptionDataTrigger> m_perception_trigger;

    // std::unique_ptr<TrafficLightTrigger> m_traffic_trigger;

    // // 触发参数
    // TrafficCollectParam m_stopline_distance;
    // TrafficCollectParam m_Traffic_change;
    // PerceptionTriggerParam perception_param;

    // viz params
    cv::Mat viz_img_;

    // 数据缓存和管理类
    std::unique_ptr<SceneManager> agent_scene_mgr_{nullptr};
    std::unique_ptr<TrafficLightManager> traffic_light_mgr_{nullptr};
    std::unique_ptr<HdmapManager> hdmap_mgr_{nullptr};
    std::unique_ptr<TaskManager> task_mgr_{nullptr};
    
    // 突然障碍物检测器
    //std::unique_ptr<SuddenObstacleDetector> sudden_obstacle_detector_{nullptr};
};

// 红绿灯状态错误检测
bool Red_to_Green_to_Red_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time = 2.0);

bool Green_to_Red_to_Green_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time = 2.0);

bool None_to_Green_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time = 2.0);

bool Green_to_None_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time = 2.0);

bool Yellow_to_Green_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time = 2.0);

bool None_to_Red_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time = 2.0);

bool Red_to_None_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time = 2.0);

#endif
