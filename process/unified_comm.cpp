#include "unified_comm.h"
#include <cerrno> // for errno
#include <cstring> // for strerror
#include <sstream>
#include <stdexcept> // for runtime_error
#include <unistd.h> // for dup, dup2, close
#include <fcntl.h>  // for open, O_WRONLY
#include "params.hpp"

// 临时屏蔽 stderr 输出
class StderrSuppressor {
public:
    StderrSuppressor() {
        stderr_backup_ = dup(STDERR_FILENO);
        int devnull = open("/dev/null", O_WRONLY);
        dup2(devnull, STDERR_FILENO);
        close(devnull);
    }
    
    ~StderrSuppressor() {
        dup2(stderr_backup_, STDERR_FILENO);
        close(stderr_backup_);
    }
    
private:
    int stderr_backup_;
};

UnifiedComm::UnifiedComm() {
    // InitQueue();
    char *ugv_com = std::getenv("XS_UGV_COM");
    if (ugv_com == nullptr) {
        std::cout << "XS_UGV_COM is not set, use RCS as default" << std::endl;
        // std::exit(EXIT_FAILURE);
    }

    if (strcmp(ugv_com, "rcs") == 0) {
        ugv_com_ = CommType_RCS;
        if (!InitRcs()) {
            tl_rcs_channel_->delete_channel();
            perception_rcs_channel_->delete_channel();
            hdmap_rcs_channel_->delete_channel();
            nml_cleanup();
            std::exit(EXIT_FAILURE);
            return;
        }
        // 初始化RCS读取函数指针数组
        read_functions_ = {[this]() { return RcsReadFusedInfo(); }, [this]() { return RcsReadLocalHdmap(); },
                           [this]() { return RcsReadTrafficLight(); }, [this]() { return RcsReadTaskList(); }};
    } else {
        ugv_com_ = CommType_XSCOM;
        if (!InitXscom()) {
            crt_perception_reader_.reset();
            crt_hdmap_reader_.reset();
            crt_trafficlight_reader_.reset();
            crt_node_.reset();
            std::exit(EXIT_FAILURE);
            return;
        }
        // LifeSignalHandler::Init();
        // 初始化XSCOM读取函数指针数组
        read_functions_ = {[this]() { return CrtReadFusedInfo(); }, [this]() { return CrtReadLocalHdmap(); },
                           [this]() { return CrtReadTrafficLight(); }, [this]() { return CrtReadTaskList(); }};
    }

    // 使用统一的读取函数创建线程
    std_thread_ = std::thread(&UnifiedComm::ReadDataToQueue, this);
}

UnifiedComm::~UnifiedComm() {
    if (std_thread_.joinable())
        std_thread_.join();
    if (ugv_com_ == CommType_RCS) {
        // delete lp_rcs_channel_;
        delete hdmap_rcs_channel_;
        delete tl_rcs_channel_;
        delete perception_rcs_channel_;
        delete tasklist_rcs_channel_;
        nml_cleanup();
    }
}

// void UnifiedComm::InitQueue() {
//     lp_deque_.clear();
//     hdmap_deque_.clear();
//     trafficlight_deque.clear();
//     perception_deque.clear();
// }

bool UnifiedComm::InitRcs() {
    nml_start();

    std::string program_path = GetProgramPath();
    std::string nml_config_file = "UGVAuto.nml";

    // 屏蔽 NML 构造时的 stderr 错误输出
    {
        StderrSuppressor suppressor;
        hdmap_rcs_channel_ = new NML(Buffer_2mb_MsgFormat, CHANNEL_LocalHDMap, NODE_NAME, nml_config_file.c_str());
    }
    if (hdmap_rcs_channel_->error_type == NML_NO_MASTER_ERROR) {

        hdmap_rcs_channel_->delete_channel();
        hdmap_rcs_channel_ =
                new NML(Buffer_2mb_MsgFormat, CHANNEL_LocalHDMap, NODE_NAME, nml_config_file.c_str(), 0, 1);
        std::string info_text = "[RCS] no master error: LocalHDMap";
        LogError("%s", info_text.c_str());
        std::cout << TimeString() << info_text << std::endl;
        info_text = "[RCS] set as master: LocalHDMap";
        LogInfo("%s", info_text.c_str());
        std::cout << TimeString() << info_text << std::endl;
    }

    // 屏蔽 NML 构造时的 stderr 错误输出
    {
        StderrSuppressor suppressor;
        tl_rcs_channel_ = new NML(Buffer_10kb_MsgFormat, CHANNEL_TrafficLightInfo, NODE_NAME, nml_config_file.c_str());
    }

    if (tl_rcs_channel_->error_type == NML_NO_MASTER_ERROR) {
        tl_rcs_channel_->delete_channel();
        tl_rcs_channel_ =
                new NML(Buffer_10kb_MsgFormat, CHANNEL_TrafficLightInfo, NODE_NAME, nml_config_file.c_str(), 0, 1);
        std::string info_text = "[RCS] no master error: TrafficLightInfo";
        LogError("%s", info_text.c_str());
        std::cout << TimeString() << info_text << std::endl;
        info_text = "[RCS] set as master: TrafficLightInfo";
        LogInfo("%s", info_text.c_str());
        std::cout << TimeString() << info_text << std::endl;
    }

    // 屏蔽 NML 构造时的 stderr 错误输出
    {
        StderrSuppressor suppressor;
        perception_rcs_channel_ = new NML(Buffer_1mb_MsgFormat, CHANNEL_LpLateFusionObjectInfo, NODE_NAME, nml_config_file.c_str());
    }
    if (perception_rcs_channel_->error_type == NML_NO_MASTER_ERROR) {
        perception_rcs_channel_->delete_channel();
        perception_rcs_channel_ =
                new NML(Buffer_1mb_MsgFormat, CHANNEL_LpLateFusionObjectInfo, NODE_NAME, nml_config_file.c_str(), 0, 1);
        std::string info_text = "[RCS] no master error: LpLateFusionObjectInfo";
        LogError("%s", info_text.c_str());
        std::cout << TimeString() << info_text << std::endl;

        info_text = "[RCS] set as master: LpLateFusionObjectInfo";
        LogInfo("%s", info_text.c_str());
        std::cout << TimeString() << info_text << std::endl;
    }

    // 屏蔽 NML 构造时的 stderr 错误输出
    {
        StderrSuppressor suppressor;
        tasklist_rcs_channel_ = new NML(Buffer_500kb_MsgFormat, CHANNEL_TaskList, NODE_NAME, nml_config_file.c_str());
    }
    if (tasklist_rcs_channel_->error_type == NML_NO_MASTER_ERROR) {
        tasklist_rcs_channel_->delete_channel();
        tasklist_rcs_channel_ =
                new NML(Buffer_500kb_MsgFormat, CHANNEL_TaskList, NODE_NAME, nml_config_file.c_str(), 0, 1);
        std::string info_text = "[RCS] no master error: TaskList";
        LogError("%s", info_text.c_str());
        std::cout << TimeString() << info_text << std::endl;

        info_text = "[RCS] set as master: TaskList";
        LogInfo("%s", info_text.c_str());
        std::cout << TimeString() << info_text << std::endl;
    }

    if (!hdmap_rcs_channel_->valid() || !tl_rcs_channel_->valid() || !perception_rcs_channel_->valid() || !tasklist_rcs_channel_->valid()) {
        std::stringstream ss;
        ss << "[RCS CRITICAL ERROR] Init failed. hdmap_rcs_channel_->valid(): " << hdmap_rcs_channel_->valid()
           << ", tl_rcs_channel_->valid(): " << tl_rcs_channel_->valid()
           << ", perception_rcs_channel_->valid(): " << perception_rcs_channel_->valid()
           << ", tasklist_rcs_channel_->valid(): " << tasklist_rcs_channel_->valid();
        LogError("%s", ss.str().c_str());
        std::cout << TimeString() << ss.str() << std::endl;
        return false;
    }

    // init data cache
    rcs_hdmap_cache_ = std::make_shared<LocalHdmapMsg>();
    rcs_tl_cache_ = std::make_shared<TrafficLightMsg>();
    rcs_fused_cache_ = std::make_shared<PerceptionMsg>();
    rcs_tasklist_cache_ = std::make_shared<TaskListMsg>();

    LogInfo("[RCS] init complete.");
    return true;
}

bool UnifiedComm::InitXscom() {
    xscom::Init(NODE_NAME);

    crt_node_ = xscom::CreateNode(NODE_NAME);
    if (crt_node_ == nullptr) {
        std::cerr << "[CyberRT] failed to init Cyber Node: " << NODE_NAME << std::endl;
        // std::exit(EXIT_FAILURE);
    }

    crt_perception_reader_ = crt_node_->CreateReader<PerceptionMsg>(CHANNEL_LpLateFusionObjectInfo, nullptr);
    if (crt_perception_reader_ == nullptr) {
        std::cerr << "[CyberRT] failed to create reader: " << CHANNEL_LpLateFusionObjectInfo << std::endl;
        LogError("[CyberRT] failed to create reader: %s", CHANNEL_LpLateFusionObjectInfo);
        // std::exit(EXIT_FAILURE);
    }

    crt_hdmap_reader_ = crt_node_->CreateReader<LocalHdmapMsg>(CHANNEL_LocalHDMap, nullptr);
    if (crt_hdmap_reader_ == nullptr) {
        std::cerr << "[CyberRT] failed to create reader: " << CHANNEL_LocalHDMap << std::endl;
        LogError("[CyberRT] failed to create reader: %s", CHANNEL_LocalHDMap);
        // std::exit(EXIT_FAILURE);
    }

    crt_trafficlight_reader_ = crt_node_->CreateReader<TrafficLightMsg>(CHANNEL_TrafficLightInfo, nullptr);
    if (crt_trafficlight_reader_ == nullptr) {
        std::cerr << "[CyberRT] failed to create reader: " << CHANNEL_TrafficLightInfo << std::endl;
        LogError("[CyberRT] failed to create reader: %s", CHANNEL_TrafficLightInfo);
        // std::exit(EXIT_FAILURE);
    }

    crt_tasklist_reader_ = crt_node_->CreateReader<TaskListMsg>(CHANNEL_TaskList, nullptr);
    if (crt_tasklist_reader_ == nullptr) {
        std::cerr << "[CyberRT] failed to create reader: " << CHANNEL_TaskList << std::endl;
        LogError("[CyberRT] failed to create reader: %s", CHANNEL_TaskList);
        // std::exit(EXIT_FAILURE);
    }

    if (!crt_perception_reader_ || !crt_hdmap_reader_ || !crt_trafficlight_reader_ || !crt_tasklist_reader_) {
        // LogError("[XSCOM] init failed. crt_perception_reader_: %p, crt_hdmap_reader_: %p, crt_trafficlight_reader_: %p, crt_tasklist_reader_: %p", crt_perception_reader_, crt_hdmap_reader_, crt_trafficlight_reader_, crt_tasklist_reader_);
        return false;
    }

    LogInfo("[XSCOM] init complete.");
    return true;
}

// 统一读取函数
void UnifiedComm::ReadDataToQueue() {
    while (!LifeSignalHandler::IsShutdown()) {
        // 遍历执行所有读取函数
        for (const auto &read_func: read_functions_) {
            read_func(); // 执行每个读取函数
        }
        usleep(100000); // 保持100ms间隔
    }
}

bool UnifiedComm::RcsReadLocalHdmap() {
    std::lock_guard<std::mutex> lock(hdmap_mutex_);
    if (BUFFER_2MB_MSG_TYPE == hdmap_rcs_channel_->read()) {
        BUFFER_2MB_MSG *hdmap_data = (BUFFER_2MB_MSG *) hdmap_rcs_channel_->get_address();
        bool data_ok = rcs_hdmap_cache_->ParseFromArray(hdmap_data->data, hdmap_data->len);
        if (data_ok) {
            // std::cout << TimeString() << "[Rcs] 🗺️current queue: " << hdmap_deque_.size() << std::endl;
            hdmap_deque_.push_back(*rcs_hdmap_cache_);
            if (hdmap_deque_.size() > kHdmapMaxQueueSize) {
                hdmap_deque_.pop_front();
            }
            return true;
        }
        return false;
    }
    return false;
}

bool UnifiedComm::CrtReadLocalHdmap() {
    std::lock_guard<std::mutex> lock(hdmap_mutex_);
    crt_hdmap_reader_->Observe();
    std::shared_ptr<LocalHdmapMsg> hdmap_msg = crt_hdmap_reader_->GetLatestObserved();
    if (nullptr != hdmap_msg) {
        // std::cout << TimeString() << "[XSCOM] 🗺️current queue: " << hdmap_deque_.size() << std::endl;
        hdmap_deque_.push_back(*hdmap_msg);
        if (hdmap_deque_.size() > kHdmapMaxQueueSize) {
            hdmap_deque_.pop_front();
        }
        return true;
    }
    return false;
}

bool UnifiedComm::RcsReadFusedInfo() {
    std::lock_guard<std::mutex> lock(perception_mutex_);
    if (BUFFER_1MB_MSG_TYPE == perception_rcs_channel_->read()) {
        BUFFER_1MB_MSG *fused_data = (BUFFER_1MB_MSG *) perception_rcs_channel_->get_address();
        bool data_ok = rcs_fused_cache_->ParseFromArray(fused_data->data, fused_data->len);
        if (data_ok) {
            // std::cout << TimeString() << "[Rcs] 🚗/🚶current queue: " << perception_deque_.size() << std::endl;
            perception_deque_.push_back(*rcs_fused_cache_);
            if (perception_deque_.size() > kPerceptionMaxQueueSize) {
                perception_deque_.pop_front();
            }
            return true;
        }
        return false;
    }
    return false;
}

bool UnifiedComm::CrtReadFusedInfo() {
    std::lock_guard<std::mutex> lock(perception_mutex_);
    crt_perception_reader_->Observe();
    std::shared_ptr<PerceptionMsg> fused_msg = crt_perception_reader_->GetLatestObserved();
    if (nullptr != fused_msg) {
        // std::cout << TimeString() << "[XSCOM] 🚗/🚶current queue: " << perception_deque_.size() << std::endl;
        perception_deque_.push_back(*fused_msg);
        if (perception_deque_.size() > kPerceptionMaxQueueSize) {
            perception_deque_.pop_front();
        }
        return true;
    }
    return false;
}

bool UnifiedComm::RcsReadTrafficLight() {
    std::lock_guard<std::mutex> lock(trafficlight_mutex_);
    if (BUFFER_10KB_MSG_TYPE == tl_rcs_channel_->read()) {
        BUFFER_10KB_MSG *trafficlight_data = (BUFFER_10KB_MSG *) tl_rcs_channel_->get_address();
        bool data_ok = rcs_tl_cache_->ParseFromArray(trafficlight_data->data, trafficlight_data->len);
        if (data_ok) {
            //std::cout << TimeString() << "[Rcs] 🚦current queue: " << trafficlight_deque_.size() << std::endl;
            trafficlight_deque_.push_back(*rcs_tl_cache_);
            if (trafficlight_deque_.size() > kTrafficLightMaxQueueSize) {
                trafficlight_deque_.pop_front();
            }
            return true;
        }
        return false;
    }
    return false;
}

bool UnifiedComm::CrtReadTrafficLight() {
    std::lock_guard<std::mutex> lock(trafficlight_mutex_);
    crt_trafficlight_reader_->Observe();
    std::shared_ptr<TrafficLightMsg> trafficlight_msg = crt_trafficlight_reader_->GetLatestObserved();
    if (nullptr != trafficlight_msg) {
        trafficlight_deque_.push_back(*trafficlight_msg);
        //std::cout << TimeString() << "[XSCOM] 🚦current queue: " << trafficlight_deque_.size() << std::endl;
        if (trafficlight_deque_.size() > kTrafficLightMaxQueueSize) {
            trafficlight_deque_.pop_front();
        }
        return true;
    }
    return false;
}

bool UnifiedComm::RcsReadTaskList() {
    std::lock_guard<std::mutex> lock(tasklist_mutex_);
    if (BUFFER_1MB_MSG_TYPE == tasklist_rcs_channel_->read()) {
        BUFFER_1MB_MSG *tasklist_data = (BUFFER_1MB_MSG *) tasklist_rcs_channel_->get_address();
        bool data_ok = rcs_tasklist_cache_->ParseFromArray(tasklist_data->data, tasklist_data->len);
        if (data_ok) {
            //std::cout << TimeString() << "[Rcs] 🚦current queue: " << trafficlight_deque_.size() << std::endl;
            tasklist_deque_.push_back(*rcs_tasklist_cache_);
            if (tasklist_deque_.size() > kTaskListMaxQueueSize) {
                tasklist_deque_.pop_front();
            }
            return true;
        }
        return false;
    }
    return false;
}

bool UnifiedComm::CrtReadTaskList() {
    std::lock_guard<std::mutex> lock(tasklist_mutex_);
    crt_tasklist_reader_->Observe();
    std::shared_ptr<TaskListMsg> tasklist_msg = crt_tasklist_reader_->GetLatestObserved();
    if (nullptr != tasklist_msg) {
        tasklist_deque_.push_back(*tasklist_msg);
        //std::cout << TimeString() << "[XSCOM] 🚦current queue: " << trafficlight_deque_.size() << std::endl;
        if (tasklist_deque_.size() > kTaskListMaxQueueSize) {
            tasklist_deque_.pop_front();
        }
        return true;
    }
    return false;
}

std::deque<PerceptionMsg> UnifiedComm::GetPerceptionQueue() {
    std::lock_guard<std::mutex> lock(perception_mutex_);
    return std::move(perception_deque_);
}

std::deque<LocalHdmapMsg> UnifiedComm::GetHDmapQueue() {
    std::lock_guard<std::mutex> lock(hdmap_mutex_);
    return std::move(hdmap_deque_);
}

std::deque<TrafficLightMsg> UnifiedComm::GetTrafficLightQueue() {
    std::lock_guard<std::mutex> lock(trafficlight_mutex_);
    return std::move(trafficlight_deque_);
}

std::deque<TaskListMsg> UnifiedComm::GetTaskListQueue() {
    std::lock_guard<std::mutex> lock(tasklist_mutex_);
    return std::move(tasklist_deque_);
}

void UnifiedComm::SendTask(TriggerParam &param) {
    xsdaq::Client client;
    xsdaq::msg::Task task;
    task.setDuration(param.duration); // 设置采集时间，秒
    task.setTaskName(param.task_name); // 设置采集名称
    for (size_t i = 0; i < param.data_sources.size(); i++) {
        task.addDataSource(param.data_sources[i]); // 添加采集目标
    }
    task.setTaskSource(param.trigger_source); // 添加采集任务发起来源
    task.addTag(param.trigger_scene, param.trigger_reason, TAG_Source_Daq); // 添加数据标签或采集原因

    [[maybe_unused]] xsdaq::ErrorCode err_code =
            client.start(task, [&]([[maybe_unused]] const xsdaq::msg::TaskError &err_msg) {
                std::cout << "On Task Resp:" << task.taskId() << std::endl;
            });
}

std::string UnifiedComm::GetProgramPath() {
    // 通过/proc/self/exe获取程序路径
    constexpr size_t MAX_PATH = 1024 * 2;
    std::string exe_path(MAX_PATH, '\0');

    ssize_t len = readlink("/proc/self/exe", &exe_path[0], MAX_PATH - 1);
    if (len == -1) {
        throw std::runtime_error("Failed to read executable path: " + std::string(strerror(errno)));
    }

    exe_path.resize(len); // 实际长度

    // 提取目录路径（移除文件名）
    size_t last_slash = exe_path.find_last_of('/');
    if (last_slash == std::string::npos) {
        throw std::runtime_error("Invalid executable path format: " + exe_path);
    }

    return exe_path.substr(0, last_slash);
}
