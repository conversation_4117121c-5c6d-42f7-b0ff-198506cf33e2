#include "proc_process.h"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstdio>
#include <ctime>
#include <iomanip>
#include <limits>
#include <memory>
#include <opencv2/core/base.hpp>
#include <opencv2/core/hal/interface.h>
#include <opencv2/core/types.hpp>
#include <opencv2/highgui.hpp>
#include <opencv2/imgproc.hpp>
#include <set>
#include <sstream>
#include <type_traits>
#include <unordered_map>
#include "base/header.pb.h"
#include "hdmap_mgr.hpp"
#include "params.hpp"
#include "scene_mgr.hpp"
#include "utils/cv_draw.hpp"
// #include "utils/emoji_renderer.hpp"
#include "utils/color_print.hpp"
#include "utils/vz_color.hpp"

ProcProcess::ProcProcess() {
    // // 加载所有启用的触发器配置（只读取enabled=true的项）
    // all_triggers_ = TriggerParamFactory::LoadConfig();
    // // 打印所有触发器的详细配置信息
    // TriggerParamFactory::PrintAllTriggers(all_triggers_);
    //
    // std::cout << "Initialized ProcProcess with " << all_triggers_.size() << " enabled triggers" << std::endl;

    // ini managers
    hdmap_mgr_ = std::make_unique<HdmapManager>();
    agent_scene_mgr_ = std::make_unique<SceneManager>();
    traffic_light_mgr_ = std::make_unique<TrafficLightManager>();
    task_mgr_ = std::make_unique<TaskManager>();

    // 初始化数据读取: 初始化结束后, 会自动启动一个线程, 读取数据到队列中
    unified_comm_ = std::make_unique<UnifiedComm>();
    // m_traffic_trigger = std::make_unique<TrafficLightTrigger>();
    // m_perception_trigger = std::make_unique<PerceptionDataTrigger>();

    if (Params::instance()->cfg.viz_enable) {
        const int height = static_cast<int>((Params::instance()->cfg.viz_front - Params::instance()->cfg.viz_rear) /
                                            Params::instance()->cfg.viz_resolution) +
                           1;
        const int width = static_cast<int>((Params::instance()->cfg.viz_right - Params::instance()->cfg.viz_left) /
                                           Params::instance()->cfg.viz_resolution) +
                          1;

        viz_img_ = cv::Mat(height, width, CV_8UC3);

        // 预加载闪电emoji以提高性能
        // EmojiRenderer::getInstance().preloadEmoji("⚡", 20);
        // EmojiRenderer::getInstance().preloadEmoji("⚡", 24);
        // EmojiRenderer::getInstance().preloadEmoji("⚡", 32);
    }
}

ProcProcess::~ProcProcess() {
    // m_data_reader.reset();
    // m_traffic_trigger.reset();
    // m_perception_trigger.reset();
}

// 逻辑处理
void ProcProcess::LoopProcess() {
    // 重置触发事件标志
    // has_trigger_event_ = false;

    // LogInfo("-----Process  \n");
    // Trigger(&TrafficLightTrigger::StopLineDistanceLogic, m_traffic_trigger.get(), m_stopline_distance);
    // Trigger(&TrafficLightTrigger::TrafficChangeLogic, m_traffic_trigger.get(), m_Traffic_change);
    // Trigger(&PerceptionDataTrigger::LossDetectObjects, m_perception_trigger.get(), perception_param);

    // 从进程中取出感知数据
    auto perception_frames = unified_comm_->GetPerceptionQueue();
    if (!perception_frames.empty()) {
        // if (std::fabs(perception_cache_list.back().header().local_pose().timestamp() - current_timestamp_) > 10.0) {
        //     // 新数据或重新回放的数据
        //     current_timestamp_ = perception_cache_list.back().header().local_pose().timestamp();
        //     //ClearManagerCache();
        // }
        // std::cout << TimeString() << " [PROCESS] 🚗/🚶 Consumed " << perception_cache_list.size() <<
        //         " perception messages" << std::endl;
        for (const auto &perception_msg: perception_frames) {
            // 处理感知数据
            agent_scene_mgr_->ParsePerceptionInfo(perception_msg);
        }

        // update header
        header_ = perception_frames.back().header();
    }

    // 从进程中取出hdmap数据
    auto hdmap_cache_list = unified_comm_->GetHDmapQueue();
    if (!hdmap_cache_list.empty()) {
        // std::cout << TimeString() << " [PROCESS] 🗺️ Consumed " << hdmap_cache_list.size() << " hdmap messages" <<
        //         std::endl;
        hdmap_mgr_->ParseLocalHDMap(hdmap_cache_list.back());

        if (header_.local_pose().timestamp() == 0.0) {
            header_ = hdmap_cache_list.back().header();
        } else if (header_.local_pose().timestamp() < hdmap_cache_list.back().header().local_pose().timestamp()) {
            // update to latest header
            header_ = hdmap_cache_list.back().header();
        }
    }

    // 从进程中取出红绿灯数据
    auto trafficlight_cache_list = unified_comm_->GetTrafficLightQueue();
    if (!trafficlight_cache_list.empty()) {
        // std::cout << TimeString() << " [PROCESS] 🚦 Consumed " << trafficlight_cache_list.size() <<
        //         " traffic light messages" << std::endl;
        for (const auto &tl_msg: trafficlight_cache_list) {
            traffic_light_mgr_->ParseTrafficLightMsg(tl_msg);
        }
    }

    auto tasklist_cache_list = unified_comm_->GetTaskListQueue();
    if (!tasklist_cache_list.empty()) {
        // for (const auto &task_msg: tasklist_cache_list) {
        task_mgr_->ParseTaskListMsg(tasklist_cache_list.back());

        if (hdmap_mgr_->HasValidData() && task_mgr_->HasValidData()) {
            UpdateFrontTurnInfo(hdmap_mgr_, task_mgr_);
        }
    }

    // 更新交通流数据
    if (!hdmap_cache_list.empty() || !perception_frames.empty()) {
        float ego_lane_speed_mps = CalcTrafficFlow(agent_scene_mgr_, hdmap_mgr_);
        hdmap_mgr_->SetEgoLaneSpeed(ego_lane_speed_mps);
    }

    // 使用perception/traffic light作为检测触发条件
    bool has_new_msg = !perception_frames.empty() || !trafficlight_cache_list.empty();

    // 只有在触发检测启用且有新消息时才进行检测
    if (Params::instance()->cfg.trigger.enabled && has_new_msg && is_trigger_mode_.load(std::memory_order_acquire)) {
        // 红绿灯类触发事件
        if (traffic_light_mgr_->HasValidData() && hdmap_mgr_->HasValidData()) {
            trigger_param_.Clear();

            //<-------------------------主体逻辑: 红绿灯触发事件------------------------->
            has_trigger_event_ = TrafficLightEvent(header_, traffic_light_mgr_, task_mgr_, hdmap_mgr_, trigger_param_);

            if (has_trigger_event_) {
                // 发送采集任务
                unified_comm_->SendTask(trigger_param_);
                // trigger_events_.insert(trigger_param_.task_name);
                trigger_events_.insert(trigger_param_.events.begin(), trigger_param_.events.end());
                std::stringstream ss;
                ss << " [TrafficLightTrigger] new task: " << trigger_param_.task_name
                   << ", reason: " << trigger_param_.trigger_reason;
                LogInfo("%s", ss.str().c_str());
                std::cout << TimeString() << COUT_BOLDRED << ss.str() << COUT_RESET << std::endl;

                // 🚀 启动等待机制，暂停触发检测
                int wait_duration = static_cast<int>(trigger_param_.duration);
                if (wait_duration <= 0)
                    wait_duration = 5; // 安全检查，默认5秒
                DataCaptureWaitThread(wait_duration);
            }
        }
    }

    // 检测目标检测异常类事件, 和目标检测异常事件
    if (has_new_msg && agent_scene_mgr_->HasValidData()) {
        // 突现障碍物事件
        bool has_sudden_obstacle_event = CheckSuddenObstacleEvent(agent_scene_mgr_, hdmap_mgr_, 5, Params::instance()->cfg.viz_enable);
        if (has_sudden_obstacle_event) {
            std::cout << TimeString() << " [ProcProcess] 突现障碍物事件" << std::endl;
        }
    }

    // 可视化
    if (Params::instance()->cfg.viz_enable && has_new_msg) {
        Visualize(hdmap_mgr_, agent_scene_mgr_, traffic_light_mgr_);
    }
}

// void ProcProcess::Trigger(bool (TrafficLightTrigger::*func)(UnifiedComm *, TrafficCollectParam),
//                           TrafficLightTrigger *obj, TrafficCollectParam param) {
//     (obj->*func)(m_data_reader.get(), param);
// }

// void ProcProcess::Trigger(bool (PerceptionDataTrigger::*func)(UnifiedComm *, PerceptionTriggerParam),
//                           PerceptionDataTrigger *obj, PerceptionTriggerParam param) {
//     (obj->*func)(m_data_reader.get(), param);
// }

void ProcProcess::ClearManagerCache() {
    agent_scene_mgr_->Clear();
    traffic_light_mgr_->Clear();
    hdmap_mgr_->Clear();
}

void ProcProcess::DataCaptureWaitThread(int time_sec) {
    // 🚀 防止重复启动等待线程
    bool expected = false;
    if (!is_thread_running_.compare_exchange_strong(expected, true)) {
        // 已有等待线程在运行，直接返回
        std::cout << TimeString() << " [TimeHandler] Wait thread already running, skipping new request" << std::endl;
        return;
    }

    // 🚀 立即禁用触发检测
    is_trigger_mode_.store(false, std::memory_order_release);

    std::cout << TimeString() << " [TimeHandler] Disabling trigger detection for " << time_sec << " seconds"
              << std::endl;

    // 🚀 创建后台等待线程
    std::thread([this, time_sec]() {
        try {
            // 等待指定时间
            std::this_thread::sleep_for(std::chrono::seconds(time_sec));

            // 重新启用触发检测
            is_trigger_mode_.store(true, std::memory_order_release);
            is_thread_running_.store(false, std::memory_order_release);
            trigger_events_.clear();

            std::cout << TimeString() << " [TimeHandler] Re-enabling trigger detection" << std::endl;
        } catch (const std::exception &e) {
            // 异常处理：确保状态正确恢复
            std::cerr << TimeString() << " [TimeHandler] Exception in wait thread: " << e.what() << std::endl;
            is_trigger_mode_.store(true, std::memory_order_release);
            is_thread_running_.store(false, std::memory_order_release);
        }
    }).detach();
}

// 红绿灯触发事件. 处理以下事件的触发:
// 1. tl_miss_detection_within_range
// 2. tl_false_detection_outside_range
// 3. tl_illogical_change
// 4. tl_anti_traffic_flow
bool ProcProcess::TrafficLightEvent(const Header &header, const std::unique_ptr<TrafficLightManager> &tl_mgr,
                                    const std::unique_ptr<TaskManager> &task_mgr,
                                    const std::unique_ptr<HdmapManager> &hdmap_mgr, TriggerParam &trigger_param) {
    // 获取当前时间戳并验证数据有效性
    const double current_timestamp = header.local_pose().timestamp();
    const double tl_timestamp = tl_mgr->GetLatestTimestamp();
    const double hdmap_timestamp = hdmap_mgr->GetTimestamp();

    // 任意两者时间差大于5秒时为无效数据
    const double max_time_diff = 5.0;
    if (std::abs(current_timestamp - tl_timestamp) > max_time_diff ||
        std::abs(current_timestamp - hdmap_timestamp) > max_time_diff ||
        std::abs(tl_timestamp - hdmap_timestamp) > max_time_diff) {
        // std::cout << TimeString() << " [TrafficLightTrigger] inconsistent timestamp: \n"
        //           << std::fixed << std::setprecision(3) << " ego ts: " << current_timestamp << "\n"
        //           << " tl ts: " << tl_timestamp << "\n"
        //           << " map ts: " << hdmap_timestamp << "\n"
        //           << " diff(ego-tl):  " << std::abs(current_timestamp - tl_timestamp) << "\n"
        //           << " diff(ego-map): " << std::abs(current_timestamp - hdmap_timestamp) << "\n"
        //           << " diff(tl-map):  " << std::abs(tl_timestamp - hdmap_timestamp) << "\n"
        //           << std::endl;

        return false;
    }

    // 提取ego车辆位置信息
    const double ego_x = header.local_pose().dr_x();
    const double ego_y = header.local_pose().dr_y();
    const HdPoint2d ego_pos(ego_x, ego_y);

    // 获取配置参数
    const auto &tl_miss_cfg = Params::instance()->cfg.tl_miss;
    const auto &tl_false_cfg = Params::instance()->cfg.tl_false;
    const auto &tl_error_cfg = Params::instance()->cfg.tl_illogical; // 检测结果不符合逻辑

    // 获取停止线数据
    const auto &future_stop_ids = hdmap_mgr->GetFutureStopLineIds();
    const auto &past_stop_ids = hdmap_mgr->GetPastStopLineIds();
    const auto &all_stop_lines = hdmap_mgr->GetStopLines();

    // 创建停止线ID到数据的映射以提高查找性能
    std::unordered_map<int32_t, const HdStopLine *> stop_line_map;
    for (const auto &stop_line: all_stop_lines) {
        if (stop_line.IsValid()) {
            stop_line_map[stop_line.id] = &stop_line;
        }
    }

    // 计算距离的辅助函数
    auto calculate_min_distance = [&](const std::vector<int32_t> &stop_ids) -> double {
        double min_distance = std::numeric_limits<double>::max();
        for (int32_t stop_id: stop_ids) {
            auto it = stop_line_map.find(stop_id);
            if (it != stop_line_map.end()) {
                const auto &stop_line = *it->second;
                // 只考虑红绿灯停止线(line_type == 2)
                if (stop_line.line_type == 2) {
                    double distance = ego_pos.DistanceTo(stop_line.GetCenterPoint());
                    min_distance = std::min(min_distance, distance);
                }
            }
        }
        return (min_distance == std::numeric_limits<double>::max() ? -1.0 : min_distance);
    };

    // 计算到前方和后方停止线的最小距离
    const double front_distance = calculate_min_distance(future_stop_ids);
    const double rear_distance = calculate_min_distance(past_stop_ids);

    // 检查是否有红绿灯检测结果: 使用时长为1.0秒的缓存是否有结果
    const bool has_detection = tl_mgr->HasDetectionInRange(1.0);

    // 1. 检查漏检情况 (tl_miss_detection_within_range)
    bool should_have_detection = false;
    if (front_distance >= 0 && front_distance <= tl_miss_cfg.near_front_stopline) {
        should_have_detection = true;
    }
    if (rear_distance >= 0 && rear_distance <= tl_miss_cfg.near_rear_stopline) {
        should_have_detection = true;
    }

    // 2. 检查误检情况 (tl_false_detection_outside_range)
    bool should_not_have_detection = false;
    bool front_far = (front_distance < 0 || front_distance > tl_false_cfg.far_from_front_stopline);
    bool rear_far = (rear_distance < 0 || rear_distance > tl_false_cfg.far_from_rear_stopline);
    if (front_far && rear_far) {
        should_not_have_detection = true;
    }

    // 触发事件检查
    std::vector<std::string> event_names;
    std::vector<std::string> event_descs;
    std::set<std::string> merged_data_srcs;

    // 漏检事件触发
    if (tl_miss_cfg.enabled && should_have_detection && !has_detection) {
        event_names.push_back(tl_miss_cfg.event_name);
        event_descs.push_back(tl_miss_cfg.description);
        for (const auto &source: tl_miss_cfg.data_sources) {
            merged_data_srcs.insert(source);
        }
    }

    // 误检事件触发
    if (tl_false_cfg.enabled && should_not_have_detection && has_detection) {
        event_names.push_back(tl_false_cfg.event_name);
        event_descs.push_back(tl_false_cfg.description);
        for (const auto &source: tl_false_cfg.data_sources) {
            merged_data_srcs.insert(source);
        }
    }

    // 检测结果的逻辑错误事件类的触发
    if (tl_error_cfg.enabled && should_have_detection && task_mgr->HasValidData()) {
        // 对每种类型的交通灯, 分别提取时序检测结果
        std::map<LightType, std::vector<LightStateInfo>> light_states_map;
        std::vector<LightType> light_types = {LightType::LIGHT_TYPE_FORWARD, LightType::LIGHT_TYPE_LEFT,
                                              LightType::LIGHT_TYPE_RIGHT, LightType::LIGHT_TYPE_UTURN};

        for (const auto &light_type: light_types) {
            std::vector<LightStateInfo> light_states;
            tl_mgr->GetLightStatesByType(light_type, light_states);
            light_states_map[light_type] = light_states;
        }

        // 检查每种灯类型是否存在状态错误
        bool is_state_illogical = false;
        for (const auto &light_state: light_states_map) {
            const auto &states = light_state.second;

            // 依次检查各种状态错误模式
            is_state_illogical = Red_to_Green_to_Red_Checker(states) || Green_to_Red_to_Green_Checker(states) ||
                                 None_to_Green_Checker(states) || Green_to_None_Checker(states) ||
                                 Yellow_to_Green_Checker(states) || None_to_Red_Checker(states) ||
                                 Red_to_None_Checker(states);

            if (is_state_illogical) {
                break;
            }
        }

        // 如果存在检测函数的情况, 则触发事件
        if (is_state_illogical) {
            event_names.push_back(tl_error_cfg.event_name);
            event_descs.push_back(tl_error_cfg.description);
            for (const auto &source: tl_error_cfg.data_sources) {
                merged_data_srcs.insert(source);
            }
        }
    }

    // 检测结果与Ego所在车流不符合的触发
    if (tl_error_cfg.enabled && should_have_detection && task_mgr->HasValidData()) {
    }


    // 创建触发参数
    if (!event_names.empty()) {
        trigger_param.events = event_names;

        // concate string
        if (!event_names.empty()) {
            trigger_param.task_name = event_names[0];
            for (size_t i = 1; i < event_names.size(); ++i) {
                trigger_param.task_name += "/" + event_names[i];
            }
        }

        // 使用最大持续时间
        trigger_param.duration = std::max(tl_miss_cfg.enabled ? static_cast<int>(tl_miss_cfg.duration) : 0,
                                          tl_false_cfg.enabled ? static_cast<int>(tl_false_cfg.duration) : 0);

        // 合并数据源（已自动去重）
        trigger_param.data_sources.assign(merged_data_srcs.begin(), merged_data_srcs.end());

        // 触发源和场景留空
        trigger_param.trigger_source = "";
        trigger_param.trigger_scene = "";

        // concate string
        if (!event_descs.empty()) {
            trigger_param.trigger_reason = event_descs[0];
            for (size_t i = 1; i < event_descs.size(); ++i) {
                trigger_param.trigger_reason += "/" + event_descs[i];
            }
        }

        return true;
    }

    return false;
}

// 通过前方车道线内运动目标的速度, 推导车道线的速度
float ProcProcess::CalcTrafficFlow(const std::unique_ptr<SceneManager> &scene_mgr,
                                   std::unique_ptr<HdmapManager> &hdmap_mgr) {
    // 1. 提取车道轮廓线: 从hdmap_mgr的前方车道线(future_landmarks), 提取定长度(阈值)的车道边界线:
    //    从第1个点开始截取长度为40m(阈值)
    const double LANE_LENGTH_THRESHOLD = 40.0; // 车道长度阈值40m
    const double LANE_WIDTH_THRESHOLD = 5.0; // 车道宽度阈值5m

    const auto &future_lane_markings = hdmap_mgr->GetFutureLaneMarkings();
    if (future_lane_markings.empty()) {
        return 0.0f;
    }

    // 提取有效车道线（长度足够）
    std::vector<HdLaneLine> valid_lanes;
    valid_lanes.reserve(future_lane_markings.size()); // 预分配内存

    for (const auto &lane: future_lane_markings) {
        if (!lane.IsValid())
            continue;

        const size_t point_count = lane.points.size();
        if (point_count < 2)
            continue;

        // 计算车道线长度
        double lane_length = 0.0;
        size_t valid_point_count = 0;

        for (size_t i = 1; i < point_count; ++i) {
            lane_length += lane.points[i].DistanceTo(lane.points[i - 1]);
            valid_point_count = i + 1;

            // 当累计长度达到阈值时，截取这部分点组成新的车道线
            if (lane_length >= LANE_LENGTH_THRESHOLD) {
                HdLaneLine new_lane = lane;
                new_lane.points.clear();
                new_lane.points.reserve(valid_point_count);

                // 添加从第1个点到当前点的所有点
                for (size_t j = 0; j < valid_point_count; ++j) {
                    new_lane.points.push_back(lane.points[j]);
                }

                valid_lanes.push_back(new_lane);
                break;
            }
        }

        // 如果整个车道线长度都不足阈值，把整段加入
        if (lane_length < LANE_LENGTH_THRESHOLD) {
            valid_lanes.push_back(lane);
        }
    }

    if (valid_lanes.empty()) {
        return 0.0f;
    }

    // 2. 使用header_.local_pose().dr_x()和header_.local_pose().dr_y()作为ego车的位置,
    // 计算ego与每条边界线的第一个点的距离
    const double ego_x = header_.local_pose().dr_x();
    const double ego_y = header_.local_pose().dr_y();
    const HdPoint2d ego_pos(ego_x, ego_y);

    // 计算ego与每条车道线第一个点的距离
    std::vector<std::pair<int, double>> lane_distances; // <sequence_id, distance>
    lane_distances.reserve(valid_lanes.size()); // 预分配内存

    for (const auto &lane: valid_lanes) {
        if (lane.points.empty())
            continue;

        double distance = ego_pos.DistanceTo(lane.points[0].point);
        lane_distances.emplace_back(lane.sequence_id, distance);
    }

    // 按距离排序
    std::sort(lane_distances.begin(), lane_distances.end(),
              [](const auto &a, const auto &b) { return a.second < b.second; });

    // 3. 确定ego所在车道
    int left_lane_seq = -1;
    int right_lane_seq = -1;

    if (lane_distances.size() >= 2) {
        // 检查最短的两个距离是否差值小于5m
        if (lane_distances[1].second - lane_distances[0].second < LANE_WIDTH_THRESHOLD) {
            // 找到相邻车道，确保序号小的为左边界
            if (lane_distances[0].first < lane_distances[1].first) {
                left_lane_seq = lane_distances[0].first;
                right_lane_seq = lane_distances[1].first;
            } else {
                left_lane_seq = lane_distances[1].first;
                right_lane_seq = lane_distances[0].first;
            }
        }
    } else if (lane_distances.size() == 1) {
        // 只找到一条车道线
        int seq_id = lane_distances[0].first;
        int total_lanes = valid_lanes.size();

        if (seq_id == 0) {
            // 最左边界线，加序号1构成最右边界线
            left_lane_seq = seq_id;
            right_lane_seq = seq_id + 1;
        } else if (seq_id == total_lanes - 1) {
            // 最右边界线，减序号1构成最左边界线
            left_lane_seq = seq_id - 1;
            right_lane_seq = seq_id;
        }
        // 如果序号在中间，无法确定左右边界，保持默认值-1
    }

    // 如果没有找到有效车道，返回false
    if (left_lane_seq == -1 || right_lane_seq == -1) {
        return 0.0f;
    }

    // 4. 构建ROI轮廓
    std::vector<HdPoint2d> roi_contour;

    // 找到对应的车道线数据
    HdLaneLine *left_lane = nullptr;
    HdLaneLine *right_lane = nullptr;

    for (auto &lane: valid_lanes) {
        if (lane.sequence_id == left_lane_seq) {
            left_lane = &lane;
        } else if (lane.sequence_id == right_lane_seq) {
            right_lane = &lane;
        }

        // 如果两个车道都找到了，可以提前退出
        if (left_lane && right_lane) {
            break;
        }
    }

    if (!left_lane || !right_lane) {
        return 0.0f;
    }

    // 预分配ROI轮廓内存
    roi_contour.reserve(left_lane->points.size() + right_lane->points.size());

    // 3. 拼接找到的最左和最右边界线, 构成轮廓:
    // - 左车道的点: 不处理
    // - 右车道的点: 逆序
    // - 左车道的点 拼接 逆序后的右车道的点, 构成封闭的轮廓线, 为traffic flow 计算的roi区域

    // 添加左车道线的点
    for (const auto &point: left_lane->points) {
        roi_contour.push_back(point.point);
    }

    // 添加右车道线的点（逆序）
    for (auto it = right_lane->points.rbegin(); it != right_lane->points.rend(); ++it) {
        roi_contour.push_back(it->point);
    }

    // 确保轮廓至少有三个点才能形成有效多边形
    if (roi_contour.size() < 3) {
        return 0.0f;
    }

    // 5. 从scene_mgr的最新帧, 提取在轮廓线的运动目标, 数据结构为<id, 速度>
    const auto *latest_frame = scene_mgr->GetLatestSceneFrame();
    if (!latest_frame) {
        return 0.0f;
    }

    std::vector<std::pair<uint32_t, float>> moving_agents; // <id, speed>
    moving_agents.reserve(latest_frame->agents.size()); // 预分配内存

    // 将ROI轮廓转换为OpenCV格式用于点包含检测
    std::vector<cv::Point2f> cv_contour;
    cv_contour.reserve(roi_contour.size()); // 预分配内存
    for (const auto &point: roi_contour) {
        cv_contour.emplace_back(static_cast<float>(point.x), static_cast<float>(point.y));
    }

    // 检查每个运动目标是否在ROI内
    for (const auto &[agent_id, agent_state]: latest_frame->agents) {
        if (!agent_state)
            continue;

        // 只考虑车辆类型的运动目标
        if (agent_state->agent_type != AgentType::VEHICLE)
            continue;

        // 检查是否为运动目标（速度大于阈值）
        if (!agent_state->IsDynamic(0.5f))
            continue;

        // 检查目标中心点是否在ROI轮廓内
        cv::Point2f agent_center(agent_state->center.x, agent_state->center.y);

        // 使用OpenCV的pointPolygonTest检查点是否在多边形内
        double result = cv::pointPolygonTest(cv_contour, agent_center, false);
        if (result >= 0) {
            // 点在多边形内或边界上
            moving_agents.emplace_back(agent_id, agent_state->linear_velocity);
        }
    }

    // 6. 计算轮廓线内所有运动目标的平均速度, 作为当前车流速度, 单位为m/s
    float ego_lane_speed = 0.0;

    if (!moving_agents.empty()) {
        float total_speed = 0.0f;
        size_t valid_count = 0;

        for (const auto &[id, speed]: moving_agents) {
            // 过滤掉异常速度值（负速度或过大的速度）
            if (speed >= 0.0f && speed <= 50.0f) {
                // 最大速度限制为50m/s (180km/h)
                total_speed += speed;
                valid_count++;
            }
        }

        if (valid_count > 0) {
            ego_lane_speed = total_speed / static_cast<float>(valid_count);
        }
    }

    // 7. 将当前车流速度设置到hdmap_mgr中
    hdmap_mgr->SetEgoLaneSpeed(ego_lane_speed);

    // 可选：添加调试信息
    // std::cout << "TrafficFlowAnalysis: Found " << moving_agents.size()
    //           << " moving vehicles, average speed: " << ego_lane_speed << " m/s" << std::endl;

    return ego_lane_speed;
}

void ProcProcess::Visualize(const std::unique_ptr<HdmapManager> &hdmap_mgr,
                            const std::unique_ptr<SceneManager> &scene_mgr,
                            const std::unique_ptr<TrafficLightManager> &tl_mgr) {
    // Clear previous frame
    viz_img_.setTo(cv::Scalar(0, 0, 0));

    // 计算图像中心点
    cv::Point2f cx_origin =
            cv::Point2f(-1.0 * Params::instance()->cfg.viz_left / Params::instance()->cfg.viz_resolution,
                        Params::instance()->cfg.viz_front / Params::instance()->cfg.viz_resolution);

    if (!hdmap_mgr->IsValid()) {
        std::cout << TimeString() << " [PROCESS] hdmap_mgr is invalid!" << std::endl;
        // return;
    }
    // 确认hdmap_mgr->IsValid()有效后，再进行后续操作
    CV_Assert(hdmap_mgr && scene_mgr && tl_mgr);

    Header header{};
    if (hdmap_mgr->IsValid()) {
        header = hdmap_mgr->GetHDMapHeader();
    }

    // 获取最新场景帧
    const auto *latest_scene = scene_mgr->GetLatestSceneFrame();
    if (latest_scene) {
        header = latest_scene->header_;
    }

    if (header.local_pose().timestamp() == 0.0) {
        std::cout << TimeString() << " [PROCESS] error: no valid data!" << std::endl;
        return;
    }

    // 坐标转换参数
    const double ego_x = header.local_pose().dr_x();
    const double ego_y = header.local_pose().dr_y();
    const double coord_angle_deg = header.local_pose().dr_heading() - 90.0;

    // 预计算旋转量
    const double cos_angle = std::cos(coord_angle_deg * M_PI / 180.0);
    const double sin_angle = std::sin(coord_angle_deg * M_PI / 180.0);
    const float resolution = Params::instance()->cfg.viz_resolution;

    // 统一坐标转换的lambda函数
    auto transformToImage = [&](double global_x, double global_y) -> cv::Point2f {
        // 转换到车体坐标系
        const double dx = global_x - ego_x;
        const double dy = global_y - ego_y;
        const double local_x = dx * cos_angle + dy * sin_angle;
        const double local_y = -dx * sin_angle + dy * cos_angle;

        // 转换到图像坐标系
        return cv::Point2f(static_cast<float>(cx_origin.x + local_x / resolution),
                           static_cast<float>(cx_origin.y - local_y / resolution));
    };

    // draw vehicle rect
    {
        std::vector<cv::Point2f> vg_rect{
                cv::Point2f(-0.5 * Params::instance()->vg.width, Params::instance()->vg.front_edge_to_center),
                cv::Point2f(0.5 * Params::instance()->vg.width, Params::instance()->vg.front_edge_to_center),
                cv::Point2f(0.5 * Params::instance()->vg.width, -1.0 * Params::instance()->vg.back_edge_to_center),
                cv::Point2f(-0.5 * Params::instance()->vg.width, -1.0 * Params::instance()->vg.back_edge_to_center)};

        // convert to image point
        for (auto &point: vg_rect) {
            point.x = static_cast<int>(cx_origin.x + point.x / Params::instance()->cfg.viz_resolution);
            point.y = static_cast<int>(cx_origin.y - point.y / Params::instance()->cfg.viz_resolution);
        }
        // draw vehicle rect
        for (size_t i = 0; i < vg_rect.size(); i++) {
            cv::line(viz_img_, vg_rect[i], vg_rect[(i + 1) % vg_rect.size()], vz::white_color, 2);
        }
    }

    // draw circle: every 20m
    for (int i = 1; i <= 10; i++) {
        float radius = i * 20.0 / Params::instance()->cfg.viz_resolution;
        cv::circle(viz_img_, cx_origin, radius, vz::grid_color, 1);
    }
    // draw vertical line and horizontal line accross origin
    cv::line(viz_img_, cv::Point2f(0, cx_origin.y), cv::Point2f(viz_img_.cols, cx_origin.y), vz::grid_color, 1);
    cv::line(viz_img_, cv::Point2f(cx_origin.x, 0), cv::Point2f(cx_origin.x, viz_img_.rows), vz::grid_color, 1);

    const auto &future_stop_line_ids = hdmap_mgr ? hdmap_mgr->GetFutureStopLineIds()
                                                 : static_cast<const std::vector<int32_t> &>(std::vector<int32_t>{});
    const auto &past_stop_line_ids = hdmap_mgr ? hdmap_mgr->GetPastStopLineIds()
                                               : static_cast<const std::vector<int32_t> &>(std::vector<int32_t>{});

    // ========== 1. 绘制HDMap数据 ==========
    if (hdmap_mgr && hdmap_mgr->IsValid()) {
        // 绘制future_lane_markings (白色)
        const auto &future_lane_markings = hdmap_mgr->GetFutureLaneMarkings();
        for (const auto &marking: future_lane_markings) {
            if (marking.points.size() < 2)
                continue;

            std::vector<cv::Point2f> img_points;
            img_points.reserve(marking.points.size());
            // std::cout << "marking.points.size(): " << marking.points.size() << std::endl;
            for (const auto &point: marking.points) {
                img_points.emplace_back(transformToImage(point.point.x, point.point.y));
                // std::cout << "(" << point.point.x << ", " << point.point.y << ")->(" << img_points.back().x << ", "
                // << img_points.back().y << ")" << std::endl;

                cv::circle(viz_img_, img_points.back(), 2, vz::future_point_color, -1);
            }
            // 绘制整条线
            // cv::polylines(viz_img_, img_points, false, vz::white_color, 1);
            for (size_t i = 1; i < img_points.size(); i++) {
                cv::line(viz_img_, img_points[i - 1], img_points[i], vz::future_lane_line_color, 1);
            }
        }

        // 绘制past_lane_markings (RGB(200,200,200)
        const auto &past_lane_markings = hdmap_mgr->GetPastLaneMarkings();
        for (const auto &marking: past_lane_markings) {
            if (marking.points.size() < 2)
                continue;

            std::vector<cv::Point2f> img_points;
            img_points.reserve(marking.points.size());
            for (const auto &point: marking.points) {
                img_points.emplace_back(transformToImage(point.point.x, point.point.y));

                cv::circle(viz_img_, img_points.back(), 2, vz::past_point_color, -1);
            }
            // cv::polylines(viz_img_, img_points, false, vz::light_gray_color, 1);
            for (size_t i = 1; i < img_points.size(); i++) {
                cv::line(viz_img_, img_points[i - 1], img_points[i], vz::past_lane_line_color, 1);
            }
        }

        // 绘制future_centerlines (深灰色)
        if (false) {
            const auto &future_centerlines = hdmap_mgr->GetFutureCenterlines();
            for (const auto &centerline: future_centerlines) {
                if (centerline.points.size() < 2)
                    continue;

                std::vector<cv::Point2f> img_points;
                img_points.reserve(centerline.points.size());
                for (const auto &point: centerline.points) {
                    img_points.emplace_back(transformToImage(point.point.x, point.point.y));
                    cv::circle(viz_img_, img_points.back(), 2, vz::future_point_color, -1);
                }
                // 绘制整条线
                // cv::polylines(viz_img_, img_points, false, vz::dark_gray_color, 1);
                for (size_t i = 1; i < img_points.size(); i++) {
                    cv::line(viz_img_, img_points[i - 1], img_points[i], vz::future_center_line_color, 1);
                }
            }

            // 绘制past_centerlines (深灰色)
            const auto &past_centerlines = hdmap_mgr->GetPastCenterlines();
            for (const auto &centerline: past_centerlines) {
                if (centerline.points.size() < 2)
                    continue;

                std::vector<cv::Point2f> img_points;
                img_points.reserve(centerline.points.size());
                for (const auto &point: centerline.points) {
                    img_points.emplace_back(transformToImage(point.point.x, point.point.y));
                    cv::circle(viz_img_, img_points.back(), 2, vz::past_point_color, -1);
                }
                // 绘制整条线
                // cv::polylines(viz_img_, img_points, false, vz::dark_gray_color, 1);
                for (size_t i = 1; i < img_points.size(); i++) {
                    cv::line(viz_img_, img_points[i - 1], img_points[i], vz::past_center_line_color, 1);
                }
            }
        }

        // 绘制stop_lines
        const auto &stop_lines = hdmap_mgr->GetStopLines();
        for (const auto &stop_line: stop_lines) {
            if (!stop_line.IsValid())
                continue;

            cv::Point2f start_pt = transformToImage(stop_line.start_point.x, stop_line.start_point.y);
            cv::Point2f end_pt = transformToImage(stop_line.end_point.x, stop_line.end_point.y);
            cv::Scalar color = vz::cyan_color;

            if (std::find(future_stop_line_ids.begin(), future_stop_line_ids.end(), stop_line.id) !=
                future_stop_line_ids.end()) {
                color = vz::magenta_color;
                // 上方绘制"S:id"
                cv::putText(viz_img_, "S:" + std::to_string(stop_line.id), start_pt + cv::Point2f(0, -10),
                            cv::FONT_HERSHEY_SIMPLEX, 0.55, color, 2);
            } else if (std::find(past_stop_line_ids.begin(), past_stop_line_ids.end(), stop_line.id) !=
                       past_stop_line_ids.end()) {
                color = vz::yellow_light_color;
                cv::putText(viz_img_, "S:" + std::to_string(stop_line.id), start_pt + cv::Point2f(0, -10),
                            cv::FONT_HERSHEY_SIMPLEX, 0.55, color, 2);
            } // else {
            //     color = vz::red_color;
            // }

            cv::line(viz_img_, start_pt, end_pt, color, 2);
        }

        // 绘制nearby_roads : 只绘制边界线. line使用灰色, 点使用light_green_color
        const auto &nearby_roads = hdmap_mgr->GetNearbyRoads();
        for (const auto &road: nearby_roads) {
            for (const auto &lane: road.lanes) {
                // 绘制左边界线
                if (lane.left_pts.size() >= 2) {
                    std::vector<cv::Point2f> img_points;
                    img_points.reserve(lane.left_pts.size());

                    for (const auto &point: lane.left_pts) {
                        cv::Point2f img_pt = transformToImage(point.x, point.y);
                        img_points.emplace_back(img_pt);
                        // 绘制边界点
                        cv::circle(viz_img_, img_pt, 2, vz::red_light_color, -1);
                    }

                    // 绘制连接线
                    for (size_t i = 1; i < img_points.size(); i++) {
                        cv::line(viz_img_, img_points[i - 1], img_points[i], vz::gray_color, 1);
                    }
                }

                // 绘制右边界线
                if (lane.right_pts.size() >= 2) {
                    std::vector<cv::Point2f> img_points;
                    img_points.reserve(lane.right_pts.size());

                    for (const auto &point: lane.right_pts) {
                        cv::Point2f img_pt = transformToImage(point.x, point.y);
                        img_points.emplace_back(img_pt);
                        // 绘制边界点
                        cv::circle(viz_img_, img_pt, 2, vz::red_light_color, -1);
                    }

                    // 绘制连接线
                    for (size_t i = 1; i < img_points.size(); i++) {
                        cv::line(viz_img_, img_points[i - 1], img_points[i], vz::gray_color, 1);
                    }
                }
            }
        }
    }

    // ========== 2. 绘制红绿灯状态 ==========
    // 检查是否有有效的交通灯数据
    const TrafficLightFrame *latest_tl_frame = tl_mgr->GetLatestFrame();
    if (!latest_tl_frame) {
        std::cout << TimeString() << " [PROCESS.Viz] no traffic light!" << std::endl;
        return;
    }
    CV_Assert(latest_tl_frame);

    // 红绿灯图标配置
    const int icon_size = 30;
    const int icon_spacing = 40;
    const int start_x = 30;
    const int start_y = 10;
    const int arrow_thickness = 2;

    // 获取灯状态颜色的lambda函数
    auto getLightColor = [](uint32_t light_state) -> cv::Scalar {
        switch (light_state) {
            case 1:
                return vz::red_light_color; // 红灯
            case 2:
                return vz::yellow_light_color; // 黄灯
            case 3:
                return vz::green_light_color; // 绿灯
            default:
                return vz::unknown_light_color; // 灰色/未知
        }
    };

    // 绘制交通灯图标组合的lambda函数（闪电图标 + 箭头图标）
    auto drawTrafficLightIcon = [&](cv::Point center, int direction, LightType light_type) {
        const int half_size = icon_size / 2 + 2;
        const int arrow_len = icon_size / 3;

        // 获取灯状态和闪烁状态
        uint32_t light_state = 0;
        int32_t flash_state = 0;

        light_state = static_cast<uint32_t>(latest_tl_frame->GetLightState(light_type));
        flash_state = static_cast<int32_t>(latest_tl_frame->GetLightFlash(light_type));


        // 确定箭头颜色：有数据时使用状态颜色，无数据时使用灰色
        cv::Scalar arrow_color = getLightColor(light_state);

        // 总是绘制箭头图标
        switch (direction) {
            case 0: // 前进箭头 (向上)
                ::drawArrow(viz_img_, cv::Point2f(center.x, center.y + arrow_len),
                            cv::Point2f(center.x, center.y - arrow_len), arrow_color, arrow_thickness);
                break;
            case 1: // 左转箭头
                ::drawArrow(viz_img_, cv::Point2f(center.x + arrow_len, center.y),
                            cv::Point2f(center.x - arrow_len, center.y), arrow_color, arrow_thickness);
                break;
            case 2: // 右转箭头
                ::drawArrow(viz_img_, cv::Point2f(center.x - arrow_len, center.y),
                            cv::Point2f(center.x + arrow_len, center.y), arrow_color, arrow_thickness);
                break;
            case 3: // 调头 (U字母)
                cv::putText(viz_img_, "U", center - cv::Point(8, -5), cv::FONT_HERSHEY_SIMPLEX, 1.0, arrow_color,
                            arrow_thickness);
                break;
        }

        // 在箭头左上角显示闪烁标志"F"
        // 确定"F"标志的颜色：当flash>0时使用黄色，否则使用灰色
        cv::Scalar flash_text_color = (flash_state > 0) ? vz::yellow_light_color : vz::unknown_light_color;

        // 计算"F"标志的位置（箭头图标左上角）
        cv::Point flash_text_pos(center.x - half_size, center.y - half_size + 12); // 略微向下偏移避免贴边

        // 绘制"F"标志
        cv::putText(viz_img_, "F", flash_text_pos, cv::FONT_HERSHEY_SIMPLEX, 0.55, flash_text_color, 2);
    };

    // 总是绘制各个方向的红绿灯状态图标
    int current_x = start_x;

    // 1. 左转 (left)
    {
        cv::Point center(current_x + icon_size / 2, start_y + icon_size / 2);
        drawTrafficLightIcon(center, 1, LightType::LIGHT_TYPE_LEFT);
        current_x += icon_spacing;
    }

    // 2. 前进 (forward)
    {
        cv::Point center(current_x + icon_size / 2, start_y + icon_size / 2);
        drawTrafficLightIcon(center, 0, LightType::LIGHT_TYPE_FORWARD);
        current_x += icon_spacing;
    }

    // 3. 右转 (right)
    {
        cv::Point center(current_x + icon_size / 2, start_y + icon_size / 2);
        drawTrafficLightIcon(center, 2, LightType::LIGHT_TYPE_RIGHT);
        current_x += icon_spacing;
    }

    // 4. 调头 (uturn)
    {
        cv::Point center(current_x + icon_size / 2, start_y + icon_size / 2);
        drawTrafficLightIcon(center, 3, LightType::LIGHT_TYPE_UTURN);
    }

    // ========== 3. 绘制Agent信息 ==========
    if (latest_scene) {
        const double future_time = 2.0; // 2秒
        for (const auto &[agent_id, agent_state]: latest_scene->agents) {
            if (!agent_state)
                continue;

            // 绘制四边框
            std::vector<cv::Point2f> rectangle;
            rectangle.reserve(4);
            for (const auto &corner: agent_state->corners) {
                rectangle.push_back(transformToImage(corner.x, corner.y));
            }

            // 绘制边界框
            for (size_t i = 0; i < rectangle.size(); ++i) {
                cv::line(viz_img_, rectangle[i], rectangle[(i + 1) % rectangle.size()], vz::agent_current_color, 2);
            }

            const cv::Point2f agent_center = transformToImage(agent_state->center.x, agent_state->center.y);
            // 绘制车头方向
            {
                float headding_len = agent_state->width;
                if (headding_len < 1.0f) {
                    headding_len = 1.0f;
                }
                if (headding_len > 5.0f) {
                    headding_len = 5.0f;
                }

                const double heading_x = agent_state->center.x + headding_len * std::cos(agent_state->heading_rad);
                const double heading_y = agent_state->center.y + headding_len * std::sin(agent_state->heading_rad);
                cv::Point2f agent_heading = transformToImage(heading_x, heading_y);

                // 绘制速度方向线
                drawArrow(viz_img_, agent_center, agent_heading, vz::agent_current_color, 2);
            }

            // 绘制速度大小
            std::stringstream ss;
            if (agent_state->linear_velocity > 0.0) {
                float future_distance = agent_state->linear_velocity * future_time;
                if (future_distance > 5.0f) {
                    future_distance = 5.0f;
                }
                const double future_x =
                        agent_state->center.x + future_distance * std::cos(agent_state->velocity_heading_rad);
                const double future_y =
                        agent_state->center.y + future_distance * std::sin(agent_state->velocity_heading_rad);
                cv::Point2f agent_future = transformToImage(future_x, future_y);

                // 绘制速度方向线
                drawArrow(viz_img_, agent_center, agent_future, vz::red_color, 2);

                ss << agent_id << ":" << std::fixed << std::setprecision(1) << agent_state->linear_velocity << "m/s";
            } else {
                ss << agent_id; //,"/" << "0.0" << "m/s"
            }

            // 绘制ID和速度文本
            std::string text = ss.str();
            cv::Point text_pos(static_cast<int>(agent_center.x), static_cast<int>(agent_center.y - 10));
            cv::putText(viz_img_, text, text_pos, cv::FONT_HERSHEY_SIMPLEX, 0.45, vz::agent_text_color, 1);
        }

        // 绘制静态障碍物的轮廓: 使用红色, 线宽2
        for (const auto &static_contour: latest_scene->static_contours) {
            if (static_contour.contour.size() < 3) {
                // 至少需要3个点才能形成封闭区域
                continue;
            }

            // 将全局坐标转换为图像坐标
            std::vector<cv::Point2f> img_points;
            img_points.reserve(static_contour.contour.size());

            for (const auto &point: static_contour.contour) {
                img_points.emplace_back(transformToImage(point.x, point.y));
            }

            // 绘制封闭的多边形轮廓
            if (img_points.size() >= 3) {
                // 将 cv::Point2f 转换为 cv::Point (整数坐标)
                std::vector<cv::Point> polygon_points;
                polygon_points.reserve(img_points.size());
                for (const auto &img_point: img_points) {
                    polygon_points.emplace_back(static_cast<int>(img_point.x), static_cast<int>(img_point.y));
                }

                // 使用 polylines 绘制封闭的多边形，第二个参数为 true 表示封闭
                std::vector<std::vector<cv::Point>> polygons = {polygon_points};
                cv::polylines(viz_img_, polygons, true, vz::red_color, 2);

                // 可选：填充半透明的颜色
                // cv::Scalar fill_color(0, 0, 255, 128); // 半透明红色
                // cv::fillPoly(viz_img_, polygons, fill_color);
            }
        }
    }

    // 左上角打印 base_header 的ego信息:
    //  第一行: 当前时间格式: 25/08/02 10:00:00[毫秒:3位数字]
    //  第二行: lp_timestamp
    //  第三行: (lp_dr_x, lp_dr_y, lp_dr_heading), (速度, 加速度), 小数后一位
    {
        const cv::Scalar pink_color = vz::dark_yellow; // 统一色
        const double font_scale = 0.55;
        const int thickness = 2;
        const int font_face = cv::FONT_HERSHEY_SIMPLEX;
        const int line_height = 25; // 行间距

        // 获取Header中的所有可用信息
        const auto &local_pose = header.local_pose();
        // const double header_timestamp = base_header.timestamp(); // Header级别时间戳
        // const int64_t seq_id = base_header.seq_id(); // 消息序列ID

        // LocalPose信息
        const double lp_timestamp = local_pose.timestamp();
        const double lp_dr_x = local_pose.dr_x();
        const double lp_dr_y = local_pose.dr_y();
        const double lp_dr_heading = local_pose.dr_heading();

        // GlobalPose信息（如果可用）
        // const auto& global_pose = base_header.global_pose();
        // const double gp_longitude = global_pose.longitude(); // 直接获取经度
        // const double gp_latitude = global_pose.latitude();   // 直接获取纬度

        // 获取速度和加速度（从LocalPose中提取）
        const double velocity = local_pose.linear_velocity(); // 线速度: m/s
        const double acceleration = local_pose.acceleration(); // 加速度: m/s²

        // 额外的加速度信息（用于调试）
        // const double wheel_acc = local_pose.wheel_acc(); // 前轮轮速加速度
        // const double non_wheel_acc = local_pose.non_wheel_acc(); // 电机加速度

        // 第一行：当前计算机时间格式化 (高性能版本)
        std::string time_str;
        {
            // 🕐 获取当前系统时间
            auto now = std::chrono::system_clock::now();
            auto time_since_epoch = now.time_since_epoch();

            // 提取秒和毫秒
            auto seconds = std::chrono::duration_cast<std::chrono::seconds>(time_since_epoch);
            auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(time_since_epoch) -
                                std::chrono::duration_cast<std::chrono::milliseconds>(seconds);

            // 转换为time_t用于格式化
            auto timestamp_sec = static_cast<time_t>(seconds.count());
            auto timestamp_ms = static_cast<int>(milliseconds.count());

            // 格式化时间字符串
            struct tm *time_info = std::localtime(&timestamp_sec);
            if (time_info) {
                char time_buffer[32];
                std::strftime(time_buffer, sizeof(time_buffer), "%y/%m/%d %H:%M:%S", time_info);
                char full_time[40];
                std::snprintf(full_time, sizeof(full_time), "%s %03d", time_buffer, timestamp_ms);
                time_str = full_time;
            } else {
                time_str = "Invalid Time";
            }
        }

        // 高效字符串构建（避免多次内存分配）- 扩展显示Header信息
        std::string line2, lp_ts_str, lp_str, lp_vel_acc_str, line6;
        {
            char buffer[150];

            // 第二行：Header时间戳和序列ID
            // std::snprintf(buffer, sizeof(buffer), "Header: ts=%.3f", base_header.timestamp();
            // line2 = buffer;

            // 第三行：LocalPose时间戳
            std::snprintf(buffer, sizeof(buffer), "lp_ts: %.3f", lp_timestamp);
            lp_ts_str = buffer;

            // 第四行：本地位置和朝向
            std::snprintf(buffer, sizeof(buffer), "lp: (%.1f, %.1f, %.1f)", lp_dr_x, lp_dr_y, lp_dr_heading);
            lp_str = buffer;

            // 第五行：速度和加速度详细信息
            std::snprintf(buffer, sizeof(buffer), "vel: %.1f km/h, acc: %.1f m/ss", velocity * 3.6, acceleration);
            lp_vel_acc_str = buffer;

            // 第六行：全局位置（经纬度）
            // if (gp_longitude != 0.0 || gp_latitude != 0.0) {
            //     std::snprintf(buffer, sizeof(buffer),
            //                  "Global: (%.6f°, %.6f°)",
            //                  gp_longitude, gp_latitude);
            // } else {
            //     std::snprintf(buffer, sizeof(buffer), "Global: (N/A, N/A)");
            // }
            // line6 = buffer;
        }

        // 高效文字绘制（左上角位置）
        const cv::Point start_pos(10, 60); // 左上角起始位置

        // 绘制文字 - 完整Header和运动信息显示
        cv::putText(viz_img_, time_str, start_pos, font_face, font_scale, pink_color, thickness);

        cv::putText(viz_img_, lp_ts_str, cv::Point(start_pos.x, start_pos.y + line_height), font_face, font_scale,
                    pink_color, thickness);

        cv::putText(viz_img_, lp_str, cv::Point(start_pos.x, start_pos.y + 2 * line_height), font_face, font_scale,
                    pink_color, thickness);

        cv::putText(viz_img_, lp_vel_acc_str, cv::Point(start_pos.x, start_pos.y + 3 * line_height), font_face,
                    font_scale, pink_color, thickness);

        // 打印触发任务名称
        size_t idx = 0;
        for (const auto &event: trigger_events_) {
            cv::putText(viz_img_, event, cv::Point(start_pos.x, start_pos.y + 4 * line_height + idx * line_height),
                        font_face, font_scale, vz::red_color, thickness);
            ++idx;
        }
    }

    cv::namedWindow(PROCESS_NAME, cv::WINDOW_AUTOSIZE);
    cv::imshow(PROCESS_NAME, viz_img_);
}

// 使用地图信息, 计算前方车道线转向信息, 并设置到task_mgr中
void ProcProcess::UpdateFrontTurnInfo(const std::unique_ptr<HdmapManager> &hdmap_mgr,
                                      std::unique_ptr<TaskManager> &task_mgr) {
    const auto &task_points = task_mgr->GetTaskPoints();
    if (task_points.empty()) {
        return; // 没有任务点，直接返回
    }

    // 获取前方车道线转向信息
    const auto &front_stop_ids = hdmap_mgr->GetFutureStopLineIds();
    // 如果没有找到前方存在停止线, 则使用当前task的第一个点的转向信息
    if (front_stop_ids.empty()) {
        task_mgr->SetFrontTurnInfo(task_mgr->GetTaskPoints().front().turn_info);
        return;
    }

    // map中的所有stop lines
    const auto &stop_lines = hdmap_mgr->GetStopLines();

    // 创建停止线ID到数据的映射以提高查找性能
    std::unordered_map<int32_t, const HdStopLine *> front_stop_line_map;
    for (const auto &stop_line: stop_lines) {
        if (stop_line.IsValid()) {
            // 检查是否是前方停止线
            auto it = std::find(front_stop_ids.begin(), front_stop_ids.end(), stop_line.id);
            if (it != front_stop_ids.end()) {
                front_stop_line_map[stop_line.id] = &stop_line;
            }
        }
    }

    // 叉积计算辅助函数：判断点是否在向量的左侧
    auto cross_product = [](const cv::Point2f &vec_start, const cv::Point2f &vec_end,
                            const cv::Point2f &point) -> double {
        // 向量 (vec_end - vec_start) 和 (point - vec_start) 的叉积
        return (vec_end.x - vec_start.x) * (point.y - vec_start.y) -
               (vec_end.y - vec_start.y) * (point.x - vec_start.x);
    };

    // 距离计算辅助函数
    auto distance_to_stopline = [](const cv::Point2f &task_point, const HdStopLine &stop_line) -> double {
        cv::Point2f center_point(static_cast<float>(stop_line.GetCenterPoint().x),
                                 static_cast<float>(stop_line.GetCenterPoint().y));
        float dx = task_point.x - center_point.x;
        float dy = task_point.y - center_point.y;
        return std::sqrt(dx * dx + dy * dy);
    };

    // 对每个前方停止线，找到最近的任务点并检查是否穿越
    TaskTurnInfo found_turn_info = TaskTurnInfo::TASK_TURN_NONE;
    bool found_valid_crossing = false;
    const double max_valid_distance = 10.0; // 最大有效距离：10米

    for (const auto &stop_pair: front_stop_line_map) {
        const auto &stop_line = *stop_pair.second;

        // 1. 找到与当前停止线距离最近的任务点
        size_t closest_point_index = 0;
        double min_distance = std::numeric_limits<double>::max();

        for (size_t i = 0; i < task_points.size(); ++i) {
            double dist = distance_to_stopline(task_points[i].xy, stop_line);
            if (dist < min_distance) {
                min_distance = dist;
                closest_point_index = i;
            }
        }

        // 2. 验证是否为有效点（距离停止线中心点小于10米）
        if (min_distance > max_valid_distance) {
            continue; // 距离太远，跳过这个停止线
        }

        // 3. 检查是否有下一个点来构成向量
        TaskPointInfo closest_point, next_point;
        if (closest_point_index >= task_points.size() - 1) {
            // continue; // 没有下一个点，检查是否有前一个点
            if (closest_point_index > 0) {
                closest_point = task_points[closest_point_index - 1];
                next_point = task_points[closest_point_index];
            } else {
                continue;
            }
        } else {
            closest_point = task_points[closest_point_index];
            next_point = task_points[closest_point_index + 1];
        }

        // 4. 用最近点和它的下一个点构成向量
        // const auto &closest_point = task_points[closest_point_index];
        // const auto &next_point = task_points[closest_point_index + 1];

        // 5. 将停止线转换为cv::Point2f格式
        cv::Point2f stop_start(static_cast<float>(stop_line.start_point.x),
                               static_cast<float>(stop_line.start_point.y));
        cv::Point2f stop_end(static_cast<float>(stop_line.end_point.x), static_cast<float>(stop_line.end_point.y));

        // 6. 检查停止线的两个端点是否在任务向量的左右两侧
        double cross1 = cross_product(closest_point.xy, next_point.xy, stop_start);
        double cross2 = cross_product(closest_point.xy, next_point.xy, stop_end);

        // 如果叉积符号不同，说明停止线的两个端点在任务向量的左右两侧，即任务向量穿过停止线
        if ((cross1 > 0 && cross2 < 0) || (cross1 < 0 && cross2 > 0)) {
            // 找到有效的穿越点，使用下一个点的转向信息
            found_turn_info = next_point.turn_info;
            found_valid_crossing = true;
            break; // 找到第一个有效穿越就停止
        }
    }

    // 设置前方转向信息
    if (found_valid_crossing) {
        // 找到有效的穿越点，使用该点的转向信息
        task_mgr->SetFrontTurnInfo(found_turn_info);
    } else {
        // 没有找到有效的穿越点，使用第一个任务点的转向信息
        task_mgr->SetFrontTurnInfo(task_points.front().turn_info);
    }
}

// 检查红绿灯与交通流的一致性
// 返回值: true: 一致, false: 不一致
bool ProcProcess::CheckTrafficLightConsistency(const std::unique_ptr<HdmapManager> &hdmap_mgr,
                                               const std::unique_ptr<TaskManager> &task_mgr,
                                               const std::unique_ptr<TrafficLightManager> &tl_mgr) {
    // 获取当前Ego所在车道的转向信息, 也为需要对比的灯的状态
    TaskTurnInfo task_turn_info = task_mgr->GetFrontTurnInfo();
    // 没有定义转向信息, 认为一致
    if (task_turn_info == TaskTurnInfo::TASK_TURN_NONE) {
        return true;
    }

    // 获取当前Ego所在车道的车流速度
    bool has_traffic_flow = hdmap_mgr->GetEgoLaneSpeed() > 1.0f;

    // 从TaskTurnInfo转换为LightType (数值不一致，需要显式转换)
    LightType ego_light_type;
    switch (task_turn_info) {
        case TaskTurnInfo::TASK_TURN_LEFT: // 1 -> 1
            ego_light_type = LightType::LIGHT_TYPE_LEFT;
            break;
        case TaskTurnInfo::TASK_TURN_RIGHT: // 3 -> 2
            ego_light_type = LightType::LIGHT_TYPE_RIGHT;
            break;
        case TaskTurnInfo::TASK_TURN_UTURN: // 4 -> 3
            ego_light_type = LightType::LIGHT_TYPE_UTURN;
            break;
        case TaskTurnInfo::TASK_TURN_FORWARD: // 2 -> 0
        case TaskTurnInfo::TASK_TURN_NONE: // 0 -> 0 (默认为直行灯)
        default:
            ego_light_type = LightType::LIGHT_TYPE_FORWARD;
            break;
    }

    LightColorState ego_light_state = LightColorState::LIGHT_NONE;
    if (!tl_mgr->GetLatestLightState(ego_light_type, ego_light_state)) {
        return true; // 没有获取到灯状态，认为一致
    }

    // 比较灯和车流速度是否一致
    //  1) 红灯时
    if (ego_light_state == LightColorState::LIGHT_RED) {
        // 车流速度为0, 认为一致
        if (!has_traffic_flow) {
            return true;
        }
        // 车流速度不为0, 认为不一致
        return false;
    }
    // 2) 绿灯时
    else if (ego_light_state == LightColorState::LIGHT_GREEN) {
        // 车流速度为0, 认为不一致
        if (!has_traffic_flow) {
            return false;
        }
        // 车流速度不为0, 认为一致
        return true;
    }
    // 3) 黄灯时
    else if (ego_light_state == LightColorState::LIGHT_YELLOW) {
        // 中间状态, 不判断
        return true;
    }

    // 4) 无状态时(没有检测结果)
    else if (ego_light_state == LightColorState::LIGHT_NONE) {
        // 无论有无车流, 认为不一致(原则上至少要有检测结果)
        return false;
    }

    // 其他没有覆盖的状态, 认为一致(理论上不会出现)
    return true;
}

// 1. 红跳到绿, 再从绿跳到红: 红灯->绿灯->红灯
//  cutoff_time: 检查时间长度. 单位: 秒
bool Red_to_Green_to_Red_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time) {
    if (light_states.empty()) {
        return false;
    }

    // 获取结束时间戳（最后一个数据的时刻）
    const double end_timestamp = light_states.back().timestamp;
    const double start_timestamp = end_timestamp - cutoff_time;

    // 在时间范围内查找状态变化序列：红->绿->红
    bool found_red = false; // 是否找到红灯
    bool found_green = false; // 在红灯之后是否找到绿灯
    bool found_final_red = false; // 在绿灯之后是否找到红灯

    // 从后往前遍历（时间从新到旧），在指定时间范围内检查状态变化
    for (auto it = light_states.rbegin(); it != light_states.rend(); ++it) {
        // 超出时间范围则停止检查
        if (it->timestamp < start_timestamp) {
            break;
        }

        const LightColorState current_state = it->light_color;

        // 状态机：红->绿->红
        if (!found_red && current_state == LightColorState::LIGHT_RED) {
            found_red = true;
        } else if (found_red && !found_green && current_state == LightColorState::LIGHT_GREEN) {
            found_green = true;
        } else if (found_red && found_green && !found_final_red && current_state == LightColorState::LIGHT_RED) {
            found_final_red = true;
            return true; // 找到完整的红->绿->红序列
        }
    }

    return false; // 未找到完整序列
};

// 2. 绿跳红后马上跳绿: 绿灯->红灯->绿灯
bool Green_to_Red_to_Green_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time) {
    if (light_states.empty()) {
        return false;
    }

    const double end_timestamp = light_states.back().timestamp;
    const double start_timestamp = end_timestamp - cutoff_time;

    // 在时间范围内查找状态变化序列：绿->红->绿
    bool found_green = false; // 是否找到绿灯
    bool found_red = false; // 在绿灯之后是否找到红灯
    bool found_final_green = false; // 在红灯之后是否找到绿灯

    for (auto it = light_states.rbegin(); it != light_states.rend(); ++it) {
        if (it->timestamp < start_timestamp) {
            break;
        }

        const LightColorState current_state = it->light_color;

        // 状态机：绿->红->绿
        if (!found_green && current_state == LightColorState::LIGHT_GREEN) {
            found_green = true;
        } else if (found_green && !found_red && current_state == LightColorState::LIGHT_RED) {
            found_red = true;
        } else if (found_green && found_red && !found_final_green && current_state == LightColorState::LIGHT_GREEN) {
            found_final_green = true;
            return true; // 找到完整的绿->红->绿序列
        }
    }

    return false;
};

// 3. 灰跳绿: 无状态->绿灯
bool None_to_Green_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time) {
    if (light_states.empty()) {
        return false;
    }

    const double end_timestamp = light_states.back().timestamp;
    const double start_timestamp = end_timestamp - cutoff_time;

    // 在时间范围内查找状态变化序列：无状态->绿灯
    bool found_none = false; // 是否找到无状态
    bool found_green = false; // 在无状态之后是否找到绿灯

    for (auto it = light_states.rbegin(); it != light_states.rend(); ++it) {
        if (it->timestamp < start_timestamp) {
            break;
        }

        const LightColorState current_state = it->light_color;

        // 状态机：无状态->绿灯
        if (!found_none && current_state == LightColorState::LIGHT_NONE) {
            found_none = true;
        } else if (found_none && !found_green && current_state == LightColorState::LIGHT_GREEN) {
            found_green = true;
            return true; // 找到完整的无状态->绿灯序列
        }
    }

    return false;
};

// 4. 绿跳灰: 绿灯->无状态
bool Green_to_None_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time) {
    if (light_states.empty()) {
        return false;
    }

    const double end_timestamp = light_states.back().timestamp;
    const double start_timestamp = end_timestamp - cutoff_time;

    // 在时间范围内查找状态变化序列：绿灯->无状态
    bool found_green = false; // 是否找到绿灯
    bool found_none = false; // 在绿灯之后是否找到无状态

    for (auto it = light_states.rbegin(); it != light_states.rend(); ++it) {
        if (it->timestamp < start_timestamp) {
            break;
        }

        const LightColorState current_state = it->light_color;

        // 状态机：绿灯->无状态
        if (!found_green && current_state == LightColorState::LIGHT_GREEN) {
            found_green = true;
        } else if (found_green && !found_none && current_state == LightColorState::LIGHT_NONE) {
            found_none = true;
            return true; // 找到完整的绿灯->无状态序列
        }
    }

    return false;
};

// 5. 黄跳绿: 黄灯->绿灯
bool Yellow_to_Green_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time) {
    if (light_states.empty()) {
        return false;
    }

    const double end_timestamp = light_states.back().timestamp;
    const double start_timestamp = end_timestamp - cutoff_time;

    // 在时间范围内查找状态变化序列：黄灯->绿灯
    bool found_yellow = false; // 是否找到黄灯
    bool found_green = false; // 在黄灯之后是否找到绿灯

    for (auto it = light_states.rbegin(); it != light_states.rend(); ++it) {
        if (it->timestamp < start_timestamp) {
            break;
        }

        const LightColorState current_state = it->light_color;

        // 状态机：黄灯->绿灯
        if (!found_yellow && current_state == LightColorState::LIGHT_YELLOW) {
            found_yellow = true;
        } else if (found_yellow && !found_green && current_state == LightColorState::LIGHT_GREEN) {
            found_green = true;
            return true; // 找到完整的黄灯->绿灯序列
        }
    }

    return false;
};

// 6. 无状态跳红灯: 无状态->红灯
bool None_to_Red_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time) {
    if (light_states.empty()) {
        return false;
    }

    const double end_timestamp = light_states.back().timestamp;
    const double start_timestamp = end_timestamp - cutoff_time;

    // 在时间范围内查找状态变化序列：无状态->红灯
    bool found_none = false; // 是否找到无状态
    bool found_red = false; // 在无状态之后是否找到红灯

    for (auto it = light_states.rbegin(); it != light_states.rend(); ++it) {
        if (it->timestamp < start_timestamp) {
            break;
        }

        const LightColorState current_state = it->light_color;

        // 状态机：无状态->红灯
        if (!found_none && current_state == LightColorState::LIGHT_NONE) {
            found_none = true;
        } else if (found_none && !found_red && current_state == LightColorState::LIGHT_RED) {
            found_red = true;
            return true; // 找到完整的无状态->红灯序列
        }
    }

    return false;
};

// 7. 红灯跳无状态: 红灯->无状态
bool Red_to_None_Checker(const std::vector<LightStateInfo> &light_states, const double cutoff_time) {
    if (light_states.empty()) {
        return false;
    }

    const double end_timestamp = light_states.back().timestamp;
    const double start_timestamp = end_timestamp - cutoff_time;

    // 在时间范围内查找状态变化序列：红灯->无状态
    bool found_red = false; // 是否存在红灯状态
    bool found_none = false; // 红灯之后变成无状态

    for (auto it = light_states.rbegin(); it != light_states.rend(); ++it) {
        if (it->timestamp < start_timestamp) {
            break;
        }

        const LightColorState current_state = it->light_color;

        // 状态机：红灯->无状态
        if (!found_red && current_state == LightColorState::LIGHT_RED) {
            found_red = true;
        } else if (found_red && !found_none && current_state == LightColorState::LIGHT_NONE) {
            found_none = true;
            return true; // 完整的红灯->无状态序列
        }
    }

    return false;
};

// cmp_frame_off_num 为基于当前帧往后找的比较帧数, 至少为1
bool ProcProcess::CheckSuddenObstacleEvent(const std::unique_ptr<SceneManager> &scene_mgr,
                                           const std::unique_ptr<HdmapManager> &hdmap_mgr,
                                           const int cmp_frame_off_num, bool debug_viz) {
    // 早期退出检查
    const auto& cached_scenes = scene_mgr->GetAllSceneFrames();
    if (cached_scenes.size() < 2) return false;

    // 预计算常量
    constexpr float resolution = 0.1f;
    constexpr float inv_resolution = 10.0f; // 1.0f / resolution
    constexpr float origin_x = 200.0f; // -(-20.0f) / 0.1f
    constexpr float origin_y = 200.0f; // 20.0f / 0.1f
    constexpr int image_size = 401; // 共用宽高
    constexpr double area_threshold = 5.0;

    // 获取ego pose信息 - 一次性提取
    const auto& pose = header_.local_pose();
    const double ego_x = pose.dr_x();
    const double ego_y = pose.dr_y();
    const double ego_heading_rad = (pose.dr_heading() - 90.0) * M_PI / 180.0;
    const float sin_rad = std::sin(ego_heading_rad), cos_rad = std::cos(ego_heading_rad);

    // 内联坐标转换 - 避免lambda开销
    auto toImageCoord = [=](double gx, double gy) noexcept -> cv::Point {
        const double dx = gx - ego_x, dy = gy - ego_y;
        return cv::Point(
            static_cast<int>(origin_x + (dx * cos_rad + dy * sin_rad) * inv_resolution + 0.5f),
            static_cast<int>(origin_y - (-dx * sin_rad + dy * cos_rad) * inv_resolution + 0.5f)
        );
    };

    // 获取帧迭代器 - 避免重复查找
    auto current_it = cached_scenes.rbegin();
    auto history_it = current_it;
    for (int i = 0; i < cmp_frame_off_num && std::next(history_it) != cached_scenes.rend(); ++i) {
        ++history_it;
    }

    // 预分配图像 - 复用内存
    static thread_local cv::Mat current_img(image_size, image_size, CV_8UC1);
    static thread_local cv::Mat history_img(image_size, image_size, CV_8UC1);
    static thread_local cv::Mat diff_img(image_size, image_size, CV_8UC1);
    static thread_local cv::Mat drivable_area(image_size, image_size, CV_8UC1);
    static thread_local std::vector<cv::Point> temp_points; // 复用向量
    
    current_img.setTo(0);
    history_img.setTo(0);

    // 直接在图像上绘制障碍物 - 避免中间容器
    auto fillObstacles = [&](cv::Mat& img, const auto& frame) {
        for (const auto& contour : frame->static_contours) {
            if (contour.contour.size() < 3) continue;
            
            temp_points.clear();
            temp_points.reserve(contour.contour.size());
            
            for (const auto& pt : contour.contour) {
                temp_points.push_back(toImageCoord(pt.x, pt.y));
            }
            
            if (temp_points.size() >= 3) {
                cv::fillPoly(img, temp_points, cv::Scalar(255));
            }
        }
    };

    fillObstacles(current_img, current_it->second);
    fillObstacles(history_img, history_it->second);

    // 构建可通行区域 - 仅在需要时
    bool has_hdmap = hdmap_mgr && hdmap_mgr->HasValidData();
    if (has_hdmap) {
        drivable_area.setTo(0);
        const auto& front_area = hdmap_mgr->GetFrontTraversableArea();
        const auto& rear_area = hdmap_mgr->GetRearTraversableArea();
        
        for (const auto* area : {&front_area, &rear_area}) {
            if (area->empty()) continue;
            
            temp_points.clear();
            temp_points.reserve(area->size());
            for (const auto& pt : *area) {
                temp_points.push_back(toImageCoord(pt.point.x, pt.point.y));
            }
            if (temp_points.size() >= 3) {
                cv::fillPoly(drivable_area, temp_points, cv::Scalar(255));
            }
        }
    } else {
        drivable_area.setTo(255);
    }

    // 差分计算 - 直接链式操作
    cv::absdiff(current_img, history_img, diff_img);
    cv::threshold(diff_img, diff_img, 10, 255, cv::THRESH_BINARY);
    cv::bitwise_and(diff_img, drivable_area, diff_img);
    
    // 轮廓面积计算 - 避免存储所有轮廓
    std::vector<std::vector<cv::Point>> contours;
    cv::findContours(diff_img, contours, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);
    
    double total_area = 0.0;
    for (const auto& c : contours) {
        total_area += cv::contourArea(c);
        if (total_area > area_threshold) break; // 早期退出
    }
    
    bool has_sudden_obstacle = (total_area > area_threshold);
    
    // 可视化 (只在需要时执行)
    if (debug_viz) {
        static thread_local cv::Mat debug_img(image_size, image_size, CV_8UC3);
        debug_img.setTo(0);
        
        // 绘制可通行区域 (绿色点)
        if (has_hdmap) {
            const auto& front_area = hdmap_mgr->GetFrontTraversableArea();
            const auto& rear_area = hdmap_mgr->GetRearTraversableArea();
            for (const auto* area : {&front_area, &rear_area}) {
                for (const auto& pt : *area) {
                    cv::circle(debug_img, toImageCoord(pt.point.x, pt.point.y), 1, cv::Scalar(0, 255, 0), -1);
                }
            }
        }
        
        // 绘制当前帧障碍物 (红色) 和新障碍物 (黄色)
        for (const auto& contour : current_it->second->static_contours) {
            if (contour.contour.size() < 3) continue;
            temp_points.clear();
            for (const auto& pt : contour.contour) {
                temp_points.push_back(toImageCoord(pt.x, pt.y));
            }
            cv::drawContours(debug_img, std::vector<std::vector<cv::Point>>{temp_points}, -1, cv::Scalar(0, 0, 255), 2);
        }
        
        cv::drawContours(debug_img, contours, -1, cv::Scalar(0, 255, 255), 2);
        
        // 显示图像
        cv::imshow("current_obstacles", current_img);
        cv::imshow("history_obstacles", history_img);
        cv::imshow("diff_obstacles", diff_img);
        cv::imshow("drivable_area", drivable_area);
        cv::imshow("debug_sudden_obstacle", debug_img);
    }

    return has_sudden_obstacle;
}

// bool ProcProcess::CheckSuddenObstacleEvent_V2(const std::unique_ptr<SceneManager> &scene_mgr, const int
// max_frame_num) {
//     if (!scene_mgr) {
//         return false;
//     }

//     // 1. 初始化检测器（如果还未初始化）
//     if (!sudden_obstacle_detector_) {
//         GridOccupancyManager::Config config;
//         config.resolution = 0.15f;           // 15cm网格分辨率
//         config.search_radius = 50.0f;        // 50m搜索半径
//         config.decay_rate = 0.2f;            // 衰减率
//         config.update_increment = 0.3f;      // 置信度增量
//         config.new_threshold = 0.6f;         // 新障碍物检测阈值
//         config.history_threshold = 0.2f;     // 历史阈值
//         config.min_area = 0.5f;              // 最小面积0.5m²
//         config.cleanup_threshold = 0.05f;    // 清理阈值
//         config.cleanup_interval = 10;        // 10帧清理一次

//         sudden_obstacle_detector_ = std::make_unique<SuddenObstacleDetector>(config);
//     }

//     // 2. 获取最新场景帧
//     const auto* latest_frame = scene_mgr->GetLatestSceneFrame();
//     if (!latest_frame) {
//         return false;
//     }

//     // 3. 获取ego位置和时间戳
//     const auto& header = latest_frame->header_;
//     const auto& ego_pose = header.local_pose();
//     const double ego_x = ego_pose.dr_x();
//     const double ego_y = ego_pose.dr_y();
//     const double timestamp = ego_pose.timestamp();

//     // 4. 获取静态障碍物数据
//     const auto& static_contours = latest_frame->static_contours;

//     // 5. 进行突然障碍物检测
//     auto new_obstacles = sudden_obstacle_detector_->ProcessFrame(static_contours, ego_x, ego_y, timestamp);

//     // 6. 处理检测结果
//     bool has_sudden_obstacle = !new_obstacles.empty();

//     if (has_sudden_obstacle) {
//         std::cout << "[SuddenObstacle] Detected " << new_obstacles.size()
//                   << " sudden obstacles at timestamp " << std::fixed << std::setprecision(3)
//                   << timestamp << std::endl;

//         // 输出每个检测到的障碍物信息
//         for (size_t i = 0; i < new_obstacles.size(); ++i) {
//             const auto& obstacle = new_obstacles[i];
//             std::cout << "  Obstacle " << i << ": "
//                       << "center=(" << std::fixed << std::setprecision(2)
//                       << obstacle.center.x << ", " << obstacle.center.y << "), "
//                       << "area=" << obstacle.area << "m², "
//                       << "confidence=" << obstacle.confidence << ", "
//                       << "contour_points=" << obstacle.contour.size() << std::endl;
//         }

//         // 可选：可视化结果（复用原有的可视化逻辑）
//         if (true) { // 开启可视化
//             VisualizeNewObstacles(new_obstacles, ego_x, ego_y, latest_frame->static_contours);
//         }
//     }

//     // 7. 输出统计信息（调试用）
//     static int debug_counter = 0;
//     if (++debug_counter % 50 == 0) { // 每50帧输出一次统计
//         auto stats = sudden_obstacle_detector_->GetStats();
//         std::cout << "[SuddenObstacle] Stats: "
//                   << "total_cells=" << stats.total_cells << ", "
//                   << "active_cells=" << stats.active_cells << ", "
//                   << "avg_confidence=" << std::fixed << std::setprecision(3)
//                   << stats.avg_confidence << std::endl;
//     }

//     return has_sudden_obstacle;
// }

// void ProcProcess::VisualizeNewObstacles(const std::vector<NewObstacleRegion>& new_obstacles,
//                                        double ego_x, double ego_y,
//                                        const std::vector<std::vector<cv::Point2f>>& all_obstacles) {
//     // 创建可视化图像（复用原有的坐标转换逻辑）
//     const float resolution = 0.25f;
//     const float left = -20.0f, right = 20.0f, front = 20.0f, rear = -20.0f;
//     const float origin_x = -1.0f * left / resolution;
//     const float origin_y = front / resolution;
//     const int image_width = static_cast<int>((right - left) / resolution + 1);
//     const int image_height = static_cast<int>((front - rear) / resolution + 1);

//     cv::Mat debug_image = cv::Mat::zeros(image_height, image_width, CV_8UC3);

//     // 坐标转换函数
//     auto transformToImage = [&](double global_x, double global_y) -> cv::Point2f {
//         const double dx = global_x - ego_x;
//         const double dy = global_y - ego_y;
//         // 简化版：假设ego朝向为0度
//         const double local_x = dx;
//         const double local_y = dy;

//         return cv::Point2f(
//             static_cast<float>(origin_x + local_x / resolution),
//             static_cast<float>(origin_y - local_y / resolution)
//         );
//     };

//     // 1. 绘制所有静态障碍物（白色）
//     for (const auto& obstacle : all_obstacles) {
//         std::vector<cv::Point> contour;
//         contour.reserve(obstacle.size());

//         for (const auto& point : obstacle) {
//             auto img_pt = transformToImage(point.x, point.y);
//             if (img_pt.x >= 0 && img_pt.x < image_width &&
//                 img_pt.y >= 0 && img_pt.y < image_height) {
//                 contour.emplace_back(static_cast<int>(img_pt.x), static_cast<int>(img_pt.y));
//             }
//         }

//         if (contour.size() >= 3) {
//             cv::fillPoly(debug_image, contour, cv::Scalar(100, 100, 100)); // 灰色填充
//             cv::polylines(debug_image, contour, true, cv::Scalar(255, 255, 255), 2); // 白色边框
//         }
//     }

//     // 2. 绘制新检测到的障碍物（红色高亮）
//     for (const auto& new_obstacle : new_obstacles) {
//         std::vector<cv::Point> contour;
//         contour.reserve(new_obstacle.contour.size());

//         for (const auto& point : new_obstacle.contour) {
//             auto img_pt = transformToImage(point.x, point.y);
//             if (img_pt.x >= 0 && img_pt.x < image_width &&
//                 img_pt.y >= 0 && img_pt.y < image_height) {
//                 contour.emplace_back(static_cast<int>(img_pt.x), static_cast<int>(img_pt.y));
//             }
//         }

//         if (contour.size() >= 3) {
//             cv::fillPoly(debug_image, contour, cv::Scalar(0, 0, 200)); // 红色填充
//             cv::polylines(debug_image, contour, true, cv::Scalar(0, 0, 255), 2); // 红色边框

//             // 标记质心位置
//             auto center_pt = transformToImage(new_obstacle.center.x, new_obstacle.center.y);
//             if (center_pt.x >= 0 && center_pt.x < image_width &&
//                 center_pt.y >= 0 && center_pt.y < image_height) {
//                 cv::circle(debug_image, cv::Point(static_cast<int>(center_pt.x), static_cast<int>(center_pt.y)),
//                           3, cv::Scalar(0, 255, 255), -1); // 黄色圆点
//             }
//         }
//     }

//     // 3. 绘制ego车辆位置（绿色十字）
//     const int ego_img_x = static_cast<int>(origin_x);
//     const int ego_img_y = static_cast<int>(origin_y);
//     cv::line(debug_image, cv::Point(ego_img_x - 5, ego_img_y), cv::Point(ego_img_x + 5, ego_img_y),
//              cv::Scalar(0, 255, 0), 2);
//     cv::line(debug_image, cv::Point(ego_img_x, ego_img_y - 5), cv::Point(ego_img_x, ego_img_y + 5),
//              cv::Scalar(0, 255, 0), 2);

//     // 4. 添加文本信息
//     const std::string info_text = "New Obstacles: " + std::to_string(new_obstacles.size());
//     cv::putText(debug_image, info_text, cv::Point(10, 20), cv::FONT_HERSHEY_SIMPLEX,
//                 0.6, cv::Scalar(0, 255, 255), 1);

//     // 显示图像
//     cv::imshow("SuddenObstacle_V2", debug_image);
// }
