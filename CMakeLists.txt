cmake_minimum_required(VERSION 3.10)

project(data_auto_trigger)

# 根据平台架构设置构建类型
MESSAGE("Target architecture: " ${CMAKE_SYSTEM_PROCESSOR})
if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64|AMD64|i386|i686")
    set(CMAKE_BUILD_TYPE Debug)
    MESSAGE("x86 platform detected, setting to Debug mode")
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm64")
    set(CMAKE_BUILD_TYPE Release)
    MESSAGE("aarch64 platform detected, setting to Release mode")
else()
    set(CMAKE_BUILD_TYPE Release)
    MESSAGE("Unknown platform, defaulting to Release mode")
endif()
MESSAGE("Build type: " ${CMAKE_BUILD_TYPE})

## 设置编译标志
#set(CMAKE_C_FLAGS_DEBUG "-O2 -g")
#set(CMAKE_CXX_FLAGS_DEBUG "-O2 -g")
#set(CMAKE_C_FLAGS_RELEASE "-O3")
#set(CMAKE_CXX_FLAGS_RELEASE "-O3")

#USE_NVTX: NVIDIA Tools Extension (NVTX), 用于性能分析
list(APPEND CMAKE_C_FLAGS "-Wall -Wextra -DUSE_NVTX")
list(APPEND CMAKE_CXX_FLAGS "-Wall -Wextra -DUSE_NVTX")

# set compile config - require C++17 (toml++ requires C++17)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Check if compiler supports C++17
include(CheckCXXCompilerFlag)
CHECK_CXX_COMPILER_FLAG("-std=c++17" COMPILER_SUPPORTS_CXX17)
if(COMPILER_SUPPORTS_CXX17)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17")
    add_definitions(-DCOMPILEDWITHC17)
    message(STATUS "--> Using C++17 standard.")
else()
    message(FATAL_ERROR "--> The compiler ${CMAKE_CXX_COMPILER} does not support C++17. Please use a newer compiler.")
endif()

# 获取Git标签信息（如果没有标签则使用commit hash）
execute_process(
        COMMAND bash "-c" "git describe --tags `git rev-list --tags --max-count=1` 2>/dev/null || echo 'v0.0 (no-tag)'"
        OUTPUT_VARIABLE GIT_TAG
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_QUIET
)
message("--> git tag: ${GIT_TAG}")
add_definitions(-DGIT_TAG="${GIT_TAG}")

execute_process(
        COMMAND bash "-c" "git rev-parse HEAD 2>/dev/null || echo 'unknown'"
        OUTPUT_VARIABLE GIT_COMMITID
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_QUIET
)
message("--> git commit id: ${GIT_COMMITID}")
add_definitions(-DGIT_COMMITID="${GIT_COMMITID}")

# get git datetime of last commit
execute_process(
        COMMAND bash "-c" "git log -1 --format=%cd --date=format:'%Y-%m-%d %H:%M:%S' 2>/dev/null || echo 'unknown'"
        OUTPUT_VARIABLE GIT_COMMIT_DATETIME
        OUTPUT_STRIP_TRAILING_WHITESPACE
        ERROR_QUIET
)
message("--> git commit datetime: ${GIT_COMMIT_DATETIME}")
add_definitions(-DGIT_COMMIT_DATETIME="${GIT_COMMIT_DATETIME}")

# Detect OS version and architecture
execute_process(COMMAND bash "-c" "lsb_release -rs | tr -d '\\n'"
        OUTPUT_VARIABLE LSB_RELEASE_VERSION
        ERROR_QUIET)
if(NOT LSB_RELEASE_VERSION)
    execute_process(COMMAND bash "-c" "cat /etc/os-release | grep VERSION_ID | cut -d'=' -f2 | tr -d '\"\\n'"
            OUTPUT_VARIABLE LSB_RELEASE_VERSION
            ERROR_QUIET)
endif()

execute_process(COMMAND bash "-c" "uname -m | tr -d '\\n'"
        OUTPUT_VARIABLE SYSTEM_ARCHITECTURE)

message(STATUS "[OSInfo] LSB_RELEASE_VERSION: ${LSB_RELEASE_VERSION}")
message(STATUS "[OSInfo] SYSTEM_ARCHITECTURE: ${SYSTEM_ARCHITECTURE}")

find_package(OpenCV REQUIRED)
find_package(PkgConfig REQUIRED)
pkg_check_modules(PANGOCAIRO REQUIRED pangocairo)

set(EXECUTABLE_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/0-bin)
set(LIBRARY_OUTPUT_PATH ${PROJECT_SOURCE_DIR}/0-lib)

# 普通头文件目录
include_directories(
        ${OpenCV_INCLUDE_DIRS}
        ${PANGOCAIRO_INCLUDE_DIRS}
        ${PROJECT_SOURCE_DIR}
        ${PROJECT_SOURCE_DIR}/src
        ${PROJECT_SOURCE_DIR}/src/public
        ${PROJECT_SOURCE_DIR}/mgr
        ${PROJECT_SOURCE_DIR}/params
        ${PROJECT_SOURCE_DIR}/trigger
        ${PROJECT_SOURCE_DIR}/utils
        ${PROJECT_SOURCE_DIR}/include
        ${PROJECT_SOURCE_DIR}/third_party
        ${PROJECT_SOURCE_DIR}/../xsproto_include
        ${PROJECT_SOURCE_DIR}/../faultlib/include
        ${PROJECT_SOURCE_DIR}/../xsmap/xsnml
)

# 第三方头文件作为系统目录（不产生警告）
include_directories(SYSTEM ${PROJECT_SOURCE_DIR}/include)

link_directories(
        ${OpenCV_LIBRARY_DIRS}
        ${LIBRARY_OUTPUT_PATH}
        /usr/local/lib
)

#add_subdirectory(third_tools)

# 主要源文件
file(GLOB SRCS 
    "src/*.cpp" "src/*.cc" "src/*.c" "src/*.hpp" "src/*.h" 
    "mgr/*.cpp" "mgr/*.hpp" 
    "params/*.cpp" "params/*.hpp" 
    "utils/*.cpp" "utils/*.hpp"
)

# inih 库源文件
file(GLOB INIH_SRCS 
    "third_party/inih-r61/*.c" 
    "third_party/inih-r61/*.h"
)

add_executable(${PROJECT_NAME} ${SRCS} ${INIH_SRCS})

message(STATUS "linked lib: ${PROJECT_SOURCE_DIR}/../../lib/${LSB_RELEASE_VERSION}_${SYSTEM_ARCHITECTURE}")

target_link_libraries(
        ${PROJECT_NAME}
        ${OpenCV_LIBS}
        ${PANGOCAIRO_LIBRARIES}
        public
        pthread
        protobuf
        rcs
        fastrtps
        fastcdr
        xscom
        #proto
        -L${PROJECT_SOURCE_DIR}/../../lib/${LSB_RELEASE_VERSION}_${SYSTEM_ARCHITECTURE} libxsproto.a
        # RCS
        -L${PROJECT_SOURCE_DIR}/../../lib/${LSB_RELEASE_VERSION}_${SYSTEM_ARCHITECTURE} libxsnml.a
)

# 定义process_name为project_name, 以便在C++中使用
add_definitions(-DPROCESS_NAME="${PROJECT_NAME}")