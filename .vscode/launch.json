{"version": "0.2.0", "configurations": [{"name": "Debug trigger", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/0-bin/data_auto_trigger", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [{"name": "XS_LOG_PATH", "value": "/home/<USER>/hwdev/dev-cc/log"}, {"name": "XS_UGV_MODEL", "value": "B1F-PC"}, {"name": "XS_UGV_COM", "value": "rcs"}], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}, {"description": "Set Disassembly Flavor to Intel", "text": "-gdb-set disassembly-flavor intel", "ignoreFailures": true}], "preLaunchTask": "CMake Build", "miDebuggerPath": "/usr/bin/gdb", "logging": {"engineLogging": false}}, {"name": "Attach to Process", "type": "cppdbg", "request": "attach", "program": "${workspaceFolder}/0-bin/data_auto_trigger", "processId": "${command:pickProcess}", "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "miDebuggerPath": "/usr/bin/gdb"}]}