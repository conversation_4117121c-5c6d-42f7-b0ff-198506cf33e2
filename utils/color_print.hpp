#ifndef COLOR_PRINT_HPP
#define COLOR_PRINT_HPP

// terminal color codes for C++ cout/print
#define COUT_RESET     "\033[0m"

// Regular Colors
#define COUT_BLACK     "\033[30m"
#define COUT_RED       "\033[31m"
#define COUT_GREEN     "\033[32m"
#define COUT_YELLOW    "\033[33m"
#define COUT_BLUE      "\033[34m"
#define COUT_MAGENTA   "\033[35m"
#define COUT_CYAN      "\033[36m"
#define COUT_WHITE     "\033[37m"

// Bold Colors
#define COUT_BOLDBLACK     "\033[1m\033[30m"
#define COUT_BOLDRED       "\033[1m\033[31m"
#define COUT_BOLDGREEN     "\033[1m\033[32m"
#define COUT_BOLDYELLOW    "\033[1m\033[33m"
#define COUT_BOLDBLUE      "\033[1m\033[34m"
#define COUT_BOLDMAGENTA   "\033[1m\033[35m"
#define COUT_BOLDCYAN      "\033[1m\033[36m"
#define COUT_BOLDWHITE     "\033[1m\033[37m"

// Background Colors
#define COUT_BG_BLACK      "\033[40m"
#define COUT_BG_RED        "\033[41m"
#define COUT_BG_GREEN      "\033[42m"
#define COUT_BG_YELLOW     "\033[43m"
#define COUT_BG_BLUE       "\033[44m"
#define COUT_BG_MAGENTA    "\033[45m"
#define COUT_BG_CYAN       "\033[46m"
#define COUT_BG_WHITE      "\033[47m"

// Bold Background Colors
#define COUT_BOLD_BG_BLACK     "\033[1m\033[40m"
#define COUT_BOLD_BG_RED       "\033[1m\033[41m"
#define COUT_BOLD_BG_GREEN     "\033[1m\033[42m"
#define COUT_BOLD_BG_YELLOW    "\033[1m\033[43m"
#define COUT_BOLD_BG_BLUE      "\033[1m\033[44m"
#define COUT_BOLD_BG_MAGENTA   "\033[1m\033[45m"
#define COUT_BOLD_BG_CYAN      "\033[1m\033[46m"
#define COUT_BOLD_BG_WHITE     "\033[1m\033[47m"

// High Intensity Colors (Bright variants)
#define COUT_BRIGHTBLACK   "\033[90m"
#define COUT_BRIGHTRED     "\033[91m"
#define COUT_BRIGHTGREEN   "\033[92m"
#define COUT_BRIGHTYELLOW  "\033[93m"
#define COUT_BRIGHTBLUE    "\033[94m"
#define COUT_BRIGHTMAGENTA "\033[95m"
#define COUT_BRIGHTCYAN    "\033[96m"
#define COUT_BRIGHTWHITE   "\033[97m"

// Bright Bold Colors
#define COUT_BRIGHT_BOLDBLACK   "\033[1m\033[90m"
#define COUT_BRIGHT_BOLDRED     "\033[1m\033[91m"
#define COUT_BRIGHT_BOLDGREEN   "\033[1m\033[92m"
#define COUT_BRIGHT_BOLDYELLOW  "\033[1m\033[93m"
#define COUT_BRIGHT_BOLDBLUE    "\033[1m\033[94m"
#define COUT_BRIGHT_BOLDMAGENTA "\033[1m\033[95m"
#define COUT_BRIGHT_BOLDCYAN    "\033[1m\033[96m"
#define COUT_BRIGHT_BOLDWHITE   "\033[1m\033[97m"

#endif // COLOR_PRINT_HPP
