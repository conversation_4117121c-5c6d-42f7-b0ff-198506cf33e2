#ifndef PARAMS_H
#define PARAMS_H

#include <string>
#include <vector>

// Configuration file names
#define _VG_PARAMS_FILE_ "VehicleModel.ini"
#define _CONFIG_FILE_ (PROCESS_NAME + std::string(".toml"))

// help functions
std::string TimeString(); // Returns current time as [HH-MM-SS:mmm]
std::string DateTimeString(); // Returns current date and time as [YYYY-MM-DD HH:MM:SS.mmm]


// 任务参数
struct TriggerParam {
    std::string task_name; //任务名称
    int duration;          //任务持续时间
    std::vector<std::string> data_sources;//采集的数据源
    std::string trigger_source; //触发源, 默认:空
    std::string trigger_scene; // 触发场景: 默认: 空
    std::string trigger_reason; //触发原因: 
    std::vector<std::string> events;//事件简述字符串(可能多个事件触发一次采集)

    void Clear() {
        task_name.clear();
        duration = 0;
        data_sources.clear();
        trigger_source.clear();
        trigger_scene.clear();
        trigger_reason.clear();
        events.clear();
    }
};

/**
 * @brief vehicle parameters
 */
struct VehicleParam {
    float width{0.0f}; // [m] Vehicle width
    float length{0.0f}; // [m] Vehicle length
    float height{0.0f}; // [m] Vehicle height
    float front_edge_to_center{0.0f}; // [m] Distance from front edge to center of front axle
    float back_edge_to_center{0.0f}; // [m] Distance from back edge to center of rear axle
};

/**
 * @brief configuration parameters
 */
struct TriggerConfig{
    bool enabled{true};
    float duration{5.0f};
    std::vector<std::string> default_data_sources;
};

struct TlMissConfig{
    bool enabled{true};
    float duration{5.0f};
    float near_front_stopline{50.0f};
    float near_rear_stopline{50.0f};
    std::string event_name{"tl_miss_detection_within_range"};
    std::string description{"在检测范围内没有红绿灯检测结果"};
    std::vector<std::string> data_sources;
};

struct TlFalseConfig{
    bool enabled{true};
    float duration{5.0f};
    float far_from_front_stopline{150.0f};
    float far_from_rear_stopline{150.0f};
    std::string event_name{"tl_false_detection_outside_range"};
    std::string description{"检测范围外检测到红绿灯"};
    std::vector<std::string> data_sources;
};

struct TlIllogicalConfig{
    bool enabled{true};
    float duration{5.0f};
    //std::vector<std::string> jump_patterns;
    std::string event_name{"tl_illogical_state_changed"};
    std::string description{"违背常识的红绿灯状态变化"};
    std::vector<std::string> data_sources;
};

struct TlTrafficFlowConfig{
    bool enabled{true};
    float duration{5.0f};
    float min_flow_speed_threshold{5.0f};
    std::string event_name{"tl_detection_anti_traffic_flow"};
    std::string description{"红绿灯结果违背实际交通流"};
    std::vector<std::string> data_sources;
};

struct ObjInstabilityConfig{
    bool enabled{true};
    float duration{5.0f};
    std::string event_name{"object_unstable_detection"};
    std::string description{"目标检测异常：类别不稳定、突现目标、无遮挡消失"};
    std::vector<std::string> data_sources;
};

struct ObjSuddenAppearanceConfig{
    bool enabled{true};
    float duration{5.0f};
    float min_distance_threshold{5.0f};
    std::string event_name{"obstacle_sudden_detection"};
    std::string description{"近距离突现异常障碍物"};
    std::vector<std::string> data_sources;
};

struct Config {
    std::string ugv_model{"B1F-PC"}; // UGV model identifier
    std::string root_path; // Path of root directory

    bool viz_enable{false}; 

    float viz_resolution{0.1f}; // display resolution, [m/pixel]
    float viz_front{100.0f}; // display front, [m]
    float viz_rear{-50.0f}; // display rear, [m]
    float viz_left{-50.0f}; // display left, [m]
    float viz_right{50.0f}; // display right, [m]

    int fault_module_type{0x03}; // Module type: SENSOR = 0x01, CHASSIS = 0x02, APPLICATION = 0x03
    int fault_module_id{0x00}; // decimal number: 0
    int fault_sub_id{0x01}; // decimal number: 1
    
    //触发器控制参数(总定义)
    TriggerConfig trigger;
    
    //各类事件触发定义
    TlMissConfig tl_miss;
    TlFalseConfig tl_false;
    TlIllogicalConfig tl_illogical;
    TlTrafficFlowConfig tl_illogic_flow;
    ObjInstabilityConfig instable_object;
    ObjSuddenAppearanceConfig sudden_obstacle;
};

/**
 * @brief Singleton class for managing parameters
 */
class Params {
public:
    static Params *instance();
    VehicleParam vg;
    Config cfg;



private:
    Params();
};

#endif // PARAMS_H
