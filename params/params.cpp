#include "params.hpp"
#include <algorithm>
#include <chrono>
#include <cstdlib> // for std::getenv, free
#include <cstring> // for strcmp
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <sstream>
#include <tomlplusplus/toml.hpp>
#include <unistd.h> // for get_current_dir_name
#include "inih-r61/ini.h"
#include "utils/color_print.hpp"

std::string TimeString() {
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::ostringstream oss;
    oss << "[" << std::put_time(std::localtime(&now_time_t), "%H-%M-%S") << ":" << std::setw(3) << std::setfill('0')
        << milliseconds.count() << "]";
    return oss.str();
}

std::string DateTimeString() {
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::ostringstream oss;
    oss << "[" << std::put_time(std::localtime(&now_time_t), "%F %T") << "." << std::setw(3) << std::setfill('0')
        << milliseconds.count() << "]";
    return oss.str();
}

// 用于INI解析的回调函数结构体
struct IniParseContext {
    VehicleParam *vg;
    const char *target_section;
};

// INI文件解析的回调函数
static int vehicle_ini_handler(void *user, const char *section, const char *name, const char *value) {
    IniParseContext *context = static_cast<IniParseContext *>(user);

    // 只处理目标section
    if (strcmp(section, context->target_section) != 0) {
        return 1; // 跳过其他section
    }

    try {
        if (strcmp(name, "vg_width") == 0) {
            context->vg->width = std::stof(value);
        } else if (strcmp(name, "vg_base_to_front") == 0) {
            context->vg->front_edge_to_center = std::stof(value);
        } else if (strcmp(name, "vg_base_to_rear") == 0) {
            context->vg->back_edge_to_center = std::stof(value);
        } else if (strcmp(name, "vg_height") == 0) {
            context->vg->height = std::stof(value);
        }
    } catch (const std::exception &e) {
        std::cerr << "Warning: Failed to parse " << name << "=" << value << " (" << e.what() << ")" << std::endl;
        return 0; // 继续解析其他参数
    }

    return 1; // 成功
}

Params *Params::instance() {
    static Params obj;
    return &obj;
}

Params::Params() {
    const char *ugv_model_env = std::getenv("XS_UGV_MODEL");
    if (ugv_model_env == nullptr) {
        std::cerr << "Error: XS_UGV_MODEL environment variable is not set!" << std::endl;
        return;
    }
    cfg.ugv_model = std::string(ugv_model_env);

    char *root_path_ptr = get_current_dir_name();
    if (root_path_ptr == nullptr) {
        std::cerr << "Error: Failed to get current directory!" << std::endl;
        return;
    }
    std::string root_path = root_path_ptr;
    free(root_path_ptr); // get_current_dir_name() 返回的内存需要手动释放
    cfg.root_path = root_path;

    // read vg params from VehicleModel.ini
    std::string vg_params_path = cfg.root_path + "/parameters/" + _VG_PARAMS_FILE_;
    std::ifstream vg_params_file(vg_params_path);
    if (!vg_params_file.good()) {
        std::cerr << "[Error] Failed to open file: " << vg_params_path << std::endl;
        return;
    }
    vg_params_file.close();

    // 使用inih库解析VehicleModel.ini
    IniParseContext context = {&vg, ugv_model_env};
    int parse_result = ini_parse(vg_params_path.c_str(), vehicle_ini_handler, &context);

    if (parse_result != 0) {
        std::cerr << "[Error] failed to parse file: " << vg_params_path;
        if (parse_result > 0) {
            std::cerr << " at line " << parse_result;
        }
        std::cerr << std::endl;
    } else {
        // 计算车辆长度
        vg.length = vg.front_edge_to_center + vg.back_edge_to_center;
        //std::cout << "[Info] vehicle parameters: " << vg_params_path << std::endl;
        // std::cout << "  Width: " << vg.width << " m" << std::endl;
        // std::cout << "  Length: " << vg.length << " m" << std::endl;
        // std::cout << "  Height: " << vg.height << " m" << std::endl;
        // std::cout << "  Front edge to center: " << vg.front_edge_to_center << " m" << std::endl;
        // std::cout << "  Back edge to center: " << vg.back_edge_to_center << " m" << std::endl;
    }


    // Read params from TOML file
    try {
        // 构建TOML配置文件路径
        std::string toml_file_path = cfg.root_path + "/parameters/" + _CONFIG_FILE_;

        // 检查文件是否存在（使用fstream方式，避免filesystem依赖）
        std::ifstream file_check(toml_file_path);
        if (!file_check.good()) {
            std::cerr << "Warning: TOML config file not found: " << toml_file_path << std::endl;
            return;
        }
        file_check.close();

        // 一次性解析TOML文件，避免重复解析
        auto config = toml::parse_file(toml_file_path);

        // 读取runtime配置
        if (auto runtime = config["runtime"]) {
            if (auto viz_enable = runtime["viz_enable"].value<bool>()) {
                cfg.viz_enable = *viz_enable;
            }
            if (auto viz_resolution = runtime["viz_resolution"].value<double>()) {
                cfg.viz_resolution = static_cast<float>(*viz_resolution);
            }
            if (auto viz_front = runtime["viz_front"].value<double>()) {
                cfg.viz_front = static_cast<float>(*viz_front);
            }
            if (auto viz_rear = runtime["viz_rear"].value<double>()) {
                cfg.viz_rear = static_cast<float>(*viz_rear);
            }
            if (auto viz_left = runtime["viz_left"].value<double>()) {
                cfg.viz_left = static_cast<float>(*viz_left);
            }
            if (auto viz_right = runtime["viz_right"].value<double>()) {
                cfg.viz_right = static_cast<float>(*viz_right);
            }
        }

        // 读取fault_module配置
        if (auto fault_module = config["fault_module"]) {
            // 处理十六进制字符串转整数
            if (auto type_str = fault_module["type"].value<std::string>()) {
                try {
                    cfg.fault_module_type = static_cast<int>(std::stoul(*type_str, nullptr, 16));
                } catch (const std::exception &e) {
                    std::cerr << "Warning: Invalid fault_module.type format: " << *type_str << std::endl;
                }
            }
            if (auto id_str = fault_module["id"].value<std::string>()) {
                try {
                    cfg.fault_module_id = static_cast<int>(std::stoul(*id_str, nullptr, 16));
                } catch (const std::exception &e) {
                    std::cerr << "Warning: Invalid fault_module.id format: " << *id_str << std::endl;
                }
            }
            if (auto sub_id_str = fault_module["sub_id"].value<std::string>()) {
                try {
                    cfg.fault_sub_id = static_cast<int>(std::stoul(*sub_id_str, nullptr, 16));
                } catch (const std::exception &e) {
                    std::cerr << "Warning: Invalid fault_module.sub_id format: " << *sub_id_str << std::endl;
                }
            }
        }

        // 读取triggers总配置(默认值)
        if (auto triggers = config["triggers"]) {

            // 读取具体触发器配置 - 使用lambda提高性能
            auto read_trigger_config = [&triggers](const char *section_name, auto &config_struct,
                                                   const TriggerConfig &default_trigger) {
                if (auto section = triggers[section_name]) {
                    // 继承默认值
                    config_struct.enabled = default_trigger.enabled;
                    config_struct.duration = default_trigger.duration;
                    config_struct.data_sources = default_trigger.default_data_sources;

                    // 读取特定配置，覆盖默认值
                    if (auto enabled = section["enabled"].value<bool>()) {
                        config_struct.enabled = *enabled;
                    }
                    if (auto duration = section["duration"].value<float>()) {
                        config_struct.duration = *duration;
                    }
                    if (auto description = section["description"].value<std::string>()) {
                        config_struct.description = *description;
                    }
                    if (auto data_sources = section["data_sources"].as_array()) {
                        config_struct.data_sources.clear();
                        config_struct.data_sources.reserve(data_sources->size());
                        for (const auto &source: *data_sources) {
                            if (auto source_str = source.template value<std::string>()) {
                                config_struct.data_sources.emplace_back(*source_str);
                            }
                        }
                    }
                }
            };

            // 触发器默认配置
            if (auto enabled = triggers["enabled"].value<bool>()) {
                cfg.trigger.enabled = *enabled;
            }
            if (auto duration = triggers["duration"].value<float>()) {
                cfg.trigger.duration = *duration;
            }
            if (auto data_sources = triggers["data_sources"].as_array()) {
                cfg.trigger.default_data_sources.clear();
                cfg.trigger.default_data_sources.reserve(data_sources->size());
                                 for (const auto &source: *data_sources) {
                     if (auto source_str = source.template value<std::string>()) {
                         cfg.trigger.default_data_sources.emplace_back(*source_str);
                     }
                 }
            }

            // TlMissConfig - 检测范围内没有红绿灯
            if (auto tl_miss = triggers["tl_miss_detection_within_range"]) {
                read_trigger_config("tl_miss_detection_within_range", cfg.tl_miss, cfg.trigger);
                if (auto near_front = tl_miss["near_front_stopline"].value<float>()) {
                    cfg.tl_miss.near_front_stopline = *near_front;
                }
                if (auto near_rear = tl_miss["near_rear_stopline"].value<float>()) {
                    cfg.tl_miss.near_rear_stopline = *near_rear;
                }
            }

            // TlFalseConfig - 检测范围外检测到红绿灯
            if (auto tl_false = triggers["tl_false_detection_outside_range"]) {
                read_trigger_config("tl_false_detection_outside_range", cfg.tl_false, cfg.trigger);
                if (auto far_front = tl_false["far_from_front_stopline"].value<float>()) {
                    cfg.tl_false.far_from_front_stopline = *far_front;
                }
                if (auto far_rear = tl_false["far_from_rear_stopline"].value<float>()) {
                    cfg.tl_false.far_from_rear_stopline = *far_rear;
                }
            }

            // TlIllogicalConfig - 不符合常识的红绿灯状态变化
            read_trigger_config("tl_illogical_change", cfg.tl_illogical, cfg.trigger);

            // TlTrafficFlowConfig - 红绿灯结果违背实际交通流
            if (auto tl_flow = triggers["tl_anti_traffic_flow"]) {
                read_trigger_config("tl_anti_traffic_flow", cfg.tl_illogic_flow, cfg.trigger);
                if (auto min_speed = tl_flow["min_flow_speed_threshold"].value<float>()) {
                    cfg.tl_illogic_flow.min_flow_speed_threshold = *min_speed;
                }
            }

            // ObjInstabilityConfig - 目标检测异常
            read_trigger_config("object_unstable_detection", cfg.instable_object, cfg.trigger);

            // ObjSuddenAppearanceConfig - 近距离突现异常障碍物
            if (auto sudden_obj = triggers["obstacle_sudden_detection"]) {
                read_trigger_config("obstacle_sudden_detection", cfg.sudden_obstacle, cfg.trigger);
                if (auto min_dist = sudden_obj["min_distance_threshold"].value<float>()) {
                    cfg.sudden_obstacle.min_distance_threshold = *min_dist;
                }
            }
        }
        
        //std::cout << "[Info] config loaded from: " << toml_file_path << std::endl;
        // 打印各种事件的config
        if (cfg.trigger.enabled) {
        std::cout << "[Info] ======= 触发器配置 =======" << std::endl;
        std::cout << "[Info] - [红绿灯类触发] " << cfg.tl_miss.description << ". enabled: " << cfg.tl_miss.enabled << std::endl;
        std::cout << "[Info] - [红绿灯类触发] " << cfg.tl_false.description << ". enabled: " << cfg.tl_false.enabled << std::endl;
        std::cout << "[Info] - [红绿灯类触发] " << cfg.tl_illogical.description << ". enabled: " << cfg.tl_illogical.enabled << std::endl;
        std::cout << "[Info] - [红绿灯类触发] " << cfg.tl_illogic_flow.description << ". enabled: " << cfg.tl_illogic_flow.enabled << std::endl;
            std::cout << "[Info] - [目标检测触发] " << cfg.instable_object.description << ". enabled: " << cfg.instable_object.enabled << std::endl;
            std::cout << "[Info] - [障碍物类触发] " << cfg.sudden_obstacle.description << ". enabled: " << cfg.sudden_obstacle.enabled << std::endl;
        }
        else{
            std::cout << COUT_BOLDRED << "[Info] 触发器已禁用" << COUT_RESET << std::endl;
        }

    } catch (const toml::parse_error &e) {
        std::cerr << "Error parsing TOML config file: " << e.description() << std::endl;
    } catch (const std::exception &e) {
        std::cerr << "Error loading TOML config: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "Unknown error occurred while loading TOML config" << std::endl;
    }
}
