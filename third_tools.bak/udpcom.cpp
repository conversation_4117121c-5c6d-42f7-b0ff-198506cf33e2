#include "udpcom.h"


CSocketUDP::CSocketUDP()
{
    m_timeout.tv_sec = 0;
    m_timeout.tv_usec = 250000;
}


CSocketUDP::~CSocketUDP()
{
    UdpRelease();
}


int CSocketUDP::UdpInit(int dst_port, const char* retransmit_ip, bool retransmit_flag)
{
    UdpRelease();
    // 首先创建接收socket
    m_socket_fd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if(m_socket_fd < 0)
    {
        std::cerr << "----------Socket creation error!" << std::endl;
        return -1;
    }
    memset(&m_socket_addr, 0, sizeof(m_socket_addr));
    m_socket_addr.sin_family = AF_INET;
    m_socket_addr.sin_port = htons(dst_port);
    m_socket_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    m_addr_length = sizeof(m_socket_addr);
    setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVTIMEO, (const void*)&m_timeout, sizeof(m_timeout));
    uint64_t recv_buff_size = 4 * 1024 * 1024;
    setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVBUF, &recv_buff_size, sizeof(recv_buff_size));
    if(bind(m_socket_fd, (const sockaddr*) &m_socket_addr, sizeof(m_socket_addr)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket bind error!" << std::endl;
        return -1;
    }
    std::cout << "Socket recv init sucess, fd is:" << m_socket_fd << ", port is:" << dst_port << std::endl;
    if(retransmit_flag)
    {
        // 创建转发socket
        m_rets_socket_fd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
        if(m_rets_socket_fd < 0)
        {
            UdpRelease();
            std::cerr << "----------Retransmit Socket creation error, wait 1s to create." << std::endl;
        }
        setsockopt(m_rets_socket_fd, SOL_SOCKET, SO_SNDTIMEO, (const void*)&m_timeout, sizeof(m_timeout));
        memset(&m_rets_socket_addr, 0, sizeof(m_rets_socket_addr));
        m_rets_socket_addr.sin_family = AF_INET;
        m_rets_socket_addr.sin_port = htons(dst_port);
        m_rets_socket_addr.sin_addr.s_addr = inet_addr(retransmit_ip);
        std::cout << "Retransmit socket init sucess, fd is:" << m_rets_socket_fd << ", port is:" << dst_port << std::endl;
    }

    return 0;
}


int CSocketUDP::UdpInit(const char *dst_ip, int dst_port)
{
    UdpRelease();
    // 创建发送socket
    m_socket_fd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if(m_socket_fd < 0)
	{
        std::cerr << "----------Socket creation error!" << std::endl;
	 	return -1;
	}
    setsockopt(m_socket_fd, SOL_SOCKET, SO_SNDTIMEO, (const void*)&m_timeout, sizeof(m_timeout));
    memset(&m_socket_addr, 0, sizeof(m_socket_addr));
    m_socket_addr.sin_family = AF_INET;
    m_socket_addr.sin_port = htons(dst_port);
    m_socket_addr.sin_addr.s_addr = inet_addr(dst_ip);
    m_addr_length = sizeof(m_socket_addr);
    std::cout << "Socket send init sucess, fd is:" << m_socket_fd << ", port is:" << dst_port << std::endl;
    return 0;
}


int CSocketUDP::UdpInit(const char* src_ip, int src_port, int recv_timeout)
{
    // 释放fd
    UdpRelease();
    // 创建socket
    m_socket_fd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if(m_socket_fd < 0)
    {
        std::cerr << "----------Socket creation error!" << std::endl;
        return -1;
    }
    // 设置超时
    m_timeout.tv_sec = recv_timeout;
    m_timeout.tv_usec = 0;
    if(setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVTIMEO, (const void*)&m_timeout, sizeof(m_timeout)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket set timeout error!" << std::endl;
        return -1;
    }
    // 设置最大接收缓存
    uint64_t recv_buff_size = 4 * 1024 * 1024;
    if(setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVBUF, &recv_buff_size, sizeof(recv_buff_size)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket set rcvbuf error!" << std::endl;
        return -1;
    }
    // 连接
    memset(&m_socket_addr, 0, sizeof(m_socket_addr));
    m_socket_addr.sin_family = AF_INET;
    m_socket_addr.sin_addr.s_addr = inet_addr(src_ip);
    m_socket_addr.sin_port = htons(src_port);
    if(connect(m_socket_fd, (sockaddr*) &m_socket_addr, sizeof(m_socket_addr)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket set connect error!" << std::endl;
        return -1;
    }
    m_connect_flag = true;
    std::cout << "Socket recv init sucess, fd is:" << m_socket_fd << ", port is:" << src_port << std::endl;

    return 0;
}


int CSocketUDP::UdpInit(const char* local_ip, const char* dst_ip, int dst_port)
{
    // 释放fd
    UdpRelease();
    // 创建socket
    m_socket_fd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if(m_socket_fd < 0)
    {
        std::cerr << "----------Socket creation error!" << std::endl;
        return -1;
    }
    // 设置端口重用
    int reuse = 1;
    if(setsockopt(m_socket_fd, SOL_SOCKET, SO_REUSEADDR, &reuse, sizeof(reuse)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket set reuse_addr error!" << std::endl;
        return -1;
    }
    // 设置超时
    if(setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVTIMEO, (const void*)&m_timeout, sizeof(m_timeout)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket set timeout error!" << std::endl;
        return -1;
    }
    // 设置最大接收缓存
    uint64_t recv_buff_size = 4 * 1024 * 1024;
    if(setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVBUF, &recv_buff_size, sizeof(recv_buff_size)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket set rcvbuf error!" << std::endl;
        return -1;
    }
    // 建立本地绑定
    memset(&m_socket_addr, 0, sizeof(m_socket_addr));
    m_socket_addr.sin_family = AF_INET;
    m_socket_addr.sin_port = htons(dst_port);
    m_socket_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    m_addr_length = sizeof(m_socket_addr);
    if(bind(m_socket_fd, (const sockaddr*) &m_socket_addr, sizeof(m_socket_addr)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket bind error!" << std::endl;
        return -1;
    }
    // 如果是组播加入组播
    std::string ip_str = std::string(dst_ip);
    int net = std::stoi(ip_str.substr(0, ip_str.find('.')));
    if (net >= 224 && net <= 239)
    {
        struct ip_mreq mreq;
        //设定指定网卡来接收组播
        mreq.imr_interface.s_addr = inet_addr(local_ip);
//        mreq.imr_interface.s_addr = htonl(INADDR_ANY);                //任意接口接收组播信息
        mreq.imr_multiaddr.s_addr = inet_addr(dst_ip);
        if (setsockopt(m_socket_fd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq)) != 0)
        {
            UdpRelease();
            std::cerr << "----------Socket set multicast error!" << std::endl;
            return -1;
        }
    }
    std::cout << "Socket recv init sucess, fd is:" << m_socket_fd << ", port is:" << dst_port << std::endl;

    return 0;
}


int CSocketUDP::UdpInit(int dst_port)
{
    UdpRelease();
    m_socket_fd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if(m_socket_fd < 0)
    {
        std::cerr << "----------Socket creation error!" << std::endl;
        return -1;
    }
    memset(&m_socket_addr, 0, sizeof(m_socket_addr));
    m_socket_addr.sin_family = AF_INET;
    m_socket_addr.sin_port = htons(dst_port);
    m_socket_addr.sin_addr.s_addr = htonl(INADDR_ANY);
    m_addr_length = sizeof(m_socket_addr);
    setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVTIMEO, (const void*)&m_timeout, sizeof(m_timeout));
    uint64_t recv_buff_size = 4 * 1024 * 1024;
    setsockopt(m_socket_fd, SOL_SOCKET, SO_RCVBUF, &recv_buff_size, sizeof(recv_buff_size));
    if(bind(m_socket_fd, (const sockaddr*) &m_socket_addr, sizeof(m_socket_addr)) < 0)
    {
        UdpRelease();
        std::cerr << "----------Socket bind error!" << std::endl;
        return -1;
    }
    std::cout << "Socket recv init sucess, fd is:" << m_socket_fd << ", port is:" << dst_port << std::endl;

    return 0;
}


int CSocketUDP::UdpRecv(const char* src_ip, char *buf, int buf_length)
{
    int recv_bytes = -1;
    sockaddr_in recv_addr;
    int recv_addr_length = sizeof(recv_addr);
    recv_bytes = recvfrom(m_socket_fd, buf, buf_length, 0, (sockaddr*)&recv_addr, (socklen_t*)&recv_addr_length);
    // 对比接收到的包是否来自于想要的ip
    if(std::strcmp(src_ip, inet_ntoa(recv_addr.sin_addr)) != 0)
        recv_bytes = -1;

    return recv_bytes;
}


int CSocketUDP::UdpRecv(char *buf, int buf_length)
{
    int recv_bytes = -1;
    sockaddr_in recv_addr;
    int recv_addr_length = sizeof(recv_addr);
    recv_bytes = recvfrom(m_socket_fd, buf, buf_length, 0, (sockaddr*)&recv_addr, (socklen_t*)&recv_addr_length);
//      std::cout << recv_bytes << " " << ntohs(recv_addr.sin_port) << " " << inet_ntoa(recv_addr.sin_addr) << std::endl;
    if(m_rets_socket_fd >= 0 && recv_bytes > -1)
    {
        int flag = sendto(m_rets_socket_fd, buf, recv_bytes, 0,
                          (const struct sockaddr*)&m_rets_socket_addr, sizeof(struct sockaddr_in));
        if(flag < 0)
        {
           std::cerr << "Send to fail, error code is:" << errno << std::endl;
        }
    }
    return recv_bytes;
}


int CSocketUDP::UdpSend(char *buf, int len)
{
    if(sendto(m_socket_fd, buf, len, 0, (const struct sockaddr *)&m_socket_addr, sizeof(struct sockaddr_in)) < 0)
    {
        std::cerr << "Send to fail, error code is:" << errno << std::endl;
        return -1;
    }

    return 0;
}


void CSocketUDP::UdpRelease()
{
    if(m_socket_fd >= 0)
    {
        close(m_socket_fd);
        m_socket_fd = -1;
    }
    if(m_rets_socket_fd >= 0)
    {
        close(m_rets_socket_fd);
        m_rets_socket_fd = -1;
    }
}
