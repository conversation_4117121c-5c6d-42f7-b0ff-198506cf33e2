#ifndef	 __UDPCOM_H__
#define  __UDPCOM_H__

#include <iostream>
#include <chrono>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <netinet/in.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <common/common.h>

#define RECV_TIMEOUT 0.25


class CSocketUDP
{
private:
    int m_socket_fd = -1;
    int m_rets_socket_fd = -1;
    bool m_connect_flag = false;
    struct sockaddr_in m_socket_addr;
    struct sockaddr_in m_rets_socket_addr;
    uint32_t m_receive_length;
    struct timeval m_timeout;
    socklen_t m_addr_length;

public:
    CSocketUDP();
    ~CSocketUDP();
    // UDP接收并转发初始化
    int UdpInit(int dst_port, const char* retransmit_ip, bool retransmit_flag);
    // UDP带连接收发初始化
    int UdpInit(const char* src_ip, int src_port, int recv_timeout);
    // UDP接收初始化
    int UdpInit(int dst_port);
    // UDP发送初始化
    int UdpInit(const char *dst_ip, int dst_port);
    // UDP组播接收初始化
    int UdpInit(const char* local_ip, const char* dst_ip, int dst_port);
    // UDP组播接收判断IP
    int UdpRecv(const char* src_ip, char *buf, int buf_length);
    // UDP接收
    int UdpRecv(char *buf, int buf_length);
    // UDP发送
    int UdpSend(char *buf, int len);
    // UDP释放
    void UdpRelease();
};
#endif
