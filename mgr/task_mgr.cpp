#include "task_mgr.hpp"

TaskManager::TaskManager() {
    // 预分配一些空间以减少重新分配
    task_points_.reserve(250);
}

TaskManager::~TaskManager() {
    // 清理资源
    task_points_.clear();
}

void TaskManager::ParseTaskListMsg(const xsproto::globalpath::TaskList &task_list) {
    // 清空之前的数据
    task_points_.clear();
    
    // 检查任务列表是否有点数据
    if (task_list.points_size() == 0) {
        return;
    }
    
    // 预分配空间
    task_points_.reserve(task_list.points_size());
    
    // 遍历解析每个任务点
    for (int i = 0; i < task_list.points_size(); ++i) {
        const auto &task_point = task_list.points(i);
        
        TaskPointInfo point_info;
        
        // 解析坐标
        point_info.xy.x = task_point.x();
        point_info.xy.y = task_point.y();
        
        // 解析角度
        point_info.angle = task_point.angle();
        
        // 解析最大速度
        point_info.max_speed = task_point.max_speed();
        
        // 解析中心线ID
        point_info.centerline_id = task_point.centerline_id();
        
        // 解析转向信息
        switch (task_point.turn_info()) {
            case 0:
                point_info.turn_info = TaskTurnInfo::TASK_TURN_NONE;
                break;
            case 1:
                point_info.turn_info = TaskTurnInfo::TASK_TURN_LEFT;
                break;
            case 2:
                point_info.turn_info = TaskTurnInfo::TASK_TURN_FORWARD;
                break;
            case 3:
                point_info.turn_info = TaskTurnInfo::TASK_TURN_RIGHT;
                break;
            case 4:
                point_info.turn_info = TaskTurnInfo::TASK_TURN_UTURN;
                break;
            default:
                point_info.turn_info = TaskTurnInfo::TASK_TURN_NONE;
                break;
        }
        
        // 添加到结果容器
        task_points_.push_back(point_info);
    }
}

const std::vector<TaskPointInfo> &TaskManager::GetTaskPoints() const {
    return task_points_;
}

