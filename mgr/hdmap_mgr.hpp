#ifndef HDMAP_MGR_HPP
#define HDMAP_MGR_HPP

#include <array>
#include <cmath>
#include <cstring>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include "hdmap/local_hdmap.pb.h"

// 使用proto中的类型别名
using LocalHDMap = xsproto::hdmap::LocalHDMap;
using MapLaneMarking = xsproto::hdmap::MapLaneMarking;
using MapCenterline = xsproto::hdmap::MapCenterline;
using MapStopLine = xsproto::hdmap::MapStopLine;
using MapRoad = xsproto::hdmap::MapRoad;
using LaneTurnType = xsproto::hdmap::LaneTurnType;
using LaneMarkingPoint = xsproto::hdmap::LaneMarkingPoint;
using MapCenterlinePoint = xsproto::hdmap::MapCenterlinePoint;

// 性能优化常量
constexpr size_t MAX_LANE_MARKINGS = 128; // 最大车道线数量
constexpr size_t MAX_CENTERLINES = 64; // 最大中心线数量
constexpr size_t MAX_STOP_LINES = 32; // 最大停止线数量
constexpr size_t MAX_NEARBY_ROADS = 16; // 最大附近道路数量
constexpr size_t MAX_STOP_LINE_IDS = 32; // 最大停止线ID数量
constexpr size_t MAX_POINTS_PER_LINE = 256; // 每条线最大点数
constexpr double EPSILON = 1e-6; // 浮点数比较精度

/**
 * @brief 2D点
 */
struct HdPoint2d {
    double x = 0.0;
    double y = 0.0;

    HdPoint2d() = default;

    constexpr HdPoint2d(double x_val, double y_val) noexcept : x(x_val), y(y_val) {}

    // 距离
    inline double DistanceTo(const HdPoint2d &other) const noexcept {
        const double dx = x - other.x;
        const double dy = y - other.y;
        return std::sqrt(dx * dx + dy * dy);
    }

    // 平方距离
    inline double SquareDistanceTo(const HdPoint2d &other) const noexcept {
        const double dx = x - other.x;
        const double dy = y - other.y;
        return dx * dx + dy * dy;
    }

    // 比较操作
    inline bool operator==(const HdPoint2d &other) const noexcept {
        return (std::abs(x - other.x) < EPSILON) && (std::abs(y - other.y) < EPSILON);
    }

    inline bool operator!=(const HdPoint2d &other) const noexcept { return !(*this == other); }
};

struct HdPoint2dEx {
    HdPoint2d point;
    LaneTurnType turn_type = LaneTurnType::NO_TURN;
    int32_t stop_line_id = 0; // 0表示无

    HdPoint2dEx() = default;

    HdPoint2dEx(double x, double y, LaneTurnType turn_type_val = LaneTurnType::NO_TURN,
                int32_t stop_line_id_val = 0) noexcept :
        point(x, y), turn_type(turn_type_val), stop_line_id(stop_line_id_val) {}

    HdPoint2dEx(const LaneMarkingPoint &proto_point) noexcept :
        point(proto_point.x(), proto_point.y()), turn_type(static_cast<LaneTurnType>(proto_point.turn_type())),
        stop_line_id(proto_point.stop_line_id()) {}

    HdPoint2dEx(const MapCenterlinePoint &proto_point) noexcept :
        point(proto_point.x(), proto_point.y()), turn_type(static_cast<LaneTurnType>(proto_point.turn_type())) {}

    // 访问方法
    // inline double x() const noexcept { return point.x; }
    // inline double y() const noexcept { return point.y; }
    // inline void set_x(double x_val) noexcept { point.x = x_val; }
    // inline void set_y(double y_val) noexcept { point.y = y_val; }

    // 距离计算
    inline double DistanceTo(const HdPoint2dEx &other) const noexcept { return point.DistanceTo(other.point); }

    inline double SquareDistanceTo(const HdPoint2dEx &other) const noexcept {
        return point.SquareDistanceTo(other.point);
    }

    // 比较操作符
    inline bool operator==(const HdPoint2dEx &other) const noexcept {
        return point == other.point && turn_type == other.turn_type && stop_line_id == other.stop_line_id;
    }

    inline bool operator!=(const HdPoint2dEx &other) const noexcept { return !(*this == other); }

    // 检查是否关联停止线
    inline bool HasStopLine() const noexcept { return stop_line_id > 0; }
};

/**
 * @brief 车道线
 */
struct HdLaneLine {
    int32_t road_id = -1;
    int32_t lane_id = -1; // 可作为唯一标识
    int32_t sequence_id = -1;
    std::vector<HdPoint2dEx> points;

    void Clear() noexcept {
        road_id = lane_id = sequence_id = -1;
        points.clear();
    }

    inline bool IsValid() const noexcept { return !points.empty() && sequence_id >= 0; }

    const HdPoint2dEx &GetStartPoint() const noexcept {
        static const HdPoint2dEx empty_point{};
        return points.empty() ? empty_point : points.front();
    }

    const HdPoint2dEx &GetEndPoint() const noexcept {
        static const HdPoint2dEx empty_point{};
        return points.empty() ? empty_point : points.back();
    }

    // 获取点数量
    inline size_t GetPointCount() const noexcept { return points.size(); }
};

/**
 * @brief 中心线
 */
struct HdCenterline {
    int32_t sequence_id = -1; // 不能作为唯一标识
    LaneTurnType overall_turn_type = LaneTurnType::NO_TURN;
    std::string turn_type_str; // 类型字符串
    std::vector<HdPoint2dEx> points; // 点集

    void Clear() noexcept {
        sequence_id = -1;
        overall_turn_type = LaneTurnType::NO_TURN;
        points.clear();
        turn_type_str.clear();
    }

    inline bool IsValid() const noexcept { return !points.empty() && sequence_id >= 0; }
    // 类型字符串获取
    const std::string &GetTurnTypeString() const { return turn_type_str; }
    inline size_t GetPointCount() const noexcept { return points.size(); }
};

/**
 * @brief 停止线
 */
struct HdStopLine {
    int32_t id = -1;
    int32_t line_type = -1;
    HdPoint2d start_point;
    HdPoint2d end_point;

    HdPoint2d center_point{};
    double length = -1.0;
    double angle = 0.0;

    HdStopLine() = default;

    HdStopLine(int32_t stop_id, double x1, double y1, double x2, double y2, int32_t type = -1) noexcept :
        id(stop_id), line_type(type), start_point(x1, y1), end_point(x2, y2) {
        UpdateGeometry();
    }

    HdStopLine(const MapStopLine &proto_stop_line) noexcept :
        id(proto_stop_line.id()), line_type(proto_stop_line.line_type()),
        start_point(proto_stop_line.ptx(0), proto_stop_line.pty(0)),
        end_point(proto_stop_line.ptx(1), proto_stop_line.pty(1)) {
        UpdateGeometry();
    }

    void Clear() noexcept {
        id = line_type = -1;
        start_point = end_point = HdPoint2d{};
        length = -1.0;
        angle = 0.0;
    }

    inline bool IsValid() const noexcept { return id >= 0 && start_point != end_point; }

    // 几何计算
    const HdPoint2d &GetCenterPoint() const noexcept { return center_point; }

    double GetLength() const noexcept { return length; }

    double GetDirectionAngle() const noexcept { return angle; }

private:
    void UpdateGeometry() noexcept {
        center_point = HdPoint2d{(start_point.x + end_point.x) * 0.5, (start_point.y + end_point.y) * 0.5};
        length = start_point.DistanceTo(end_point);
        angle = std::atan2(end_point.y - start_point.y, end_point.x - start_point.x);
    }
};

struct HdMapLane {
    int32_t lane_id = -1;
    std::vector<HdPoint2d> center_pts; // 实际上没有填值
    std::vector<HdPoint2d> left_pts;
    std::vector<HdPoint2d> right_pts;
};

struct HdMapRoad {
    int32_t road_id = -1;
    std::vector<HdMapLane> lanes;
};

/**
 * @brief 高性能HDMap解析结果结构
 */
struct HDMapElemInfo {
    xsproto::base::Header header;
    // 状态信息
    double timestamp = 0.0;
    // ego所在车道的速度
    float ego_lane_speed = 0.0;

    // 主数据存储
    std::vector<HdLaneLine> future_lane_markings;
    std::vector<HdLaneLine> past_lane_markings;
    std::vector<HdCenterline> future_centerlines;
    std::vector<HdCenterline> past_centerlines;
    std::vector<HdStopLine> stop_lines;
    // 附近的道路数据
    std::vector<HdMapRoad> nearby_roads;

    // future_land_mark的stop_line id (保持插入顺序，自动去重)
    std::vector<int32_t> future_stop_line_ids;
    // past_land_mark的stop_line id (保持插入顺序，自动去重)
    std::vector<int32_t> past_stop_line_ids;

    // 前方可通行区域: future_lane_markings的第1条线, 和最后一条线倒序后, 拼接而成
    std::vector<HdPoint2dEx> front_traversable_area;
    // 后方可通行区域: past_lane_markings的第1条线, 和最后一条线倒序后, 拼接而成
    std::vector<HdPoint2dEx> rear_traversable_area;

    HDMapElemInfo() { Reserve(); }

    void Reserve() {
        future_lane_markings.reserve(MAX_LANE_MARKINGS);
        past_lane_markings.reserve(MAX_LANE_MARKINGS);
        future_centerlines.reserve(MAX_CENTERLINES);
        past_centerlines.reserve(MAX_CENTERLINES);
        stop_lines.reserve(MAX_STOP_LINES);
        future_stop_line_ids.reserve(MAX_STOP_LINE_IDS);
        past_stop_line_ids.reserve(MAX_STOP_LINE_IDS);
        nearby_roads.reserve(MAX_NEARBY_ROADS);
        front_traversable_area.reserve(500); // 预估前方可通行区域点数
        rear_traversable_area.reserve(500);  // 预估后方可通行区域点数
    }

    void Reset() noexcept {
        // reset header - 使用protobuf的Clear()方法或默认构造
        header.Clear();

        future_lane_markings.clear();
        past_lane_markings.clear();
        future_centerlines.clear();
        past_centerlines.clear();
        stop_lines.clear();
        future_stop_line_ids.clear();
        past_stop_line_ids.clear();
        nearby_roads.clear();
        front_traversable_area.clear();
        rear_traversable_area.clear();
        timestamp = 0.0;
    }
};

/**
 * @brief HDMap管理类
 */
class HdmapManager {
public:
    HdmapManager() = default;

    ~HdmapManager() = default;

    HdmapManager(const HdmapManager &) = delete;

    HdmapManager &operator=(const HdmapManager &) = delete;

    // 移动构造和赋值
    HdmapManager(HdmapManager &&) = default;

    HdmapManager &operator=(HdmapManager &&) = default;

    /**
     * @brief 解析LocalHDMap数据
     */
    bool ParseLocalHDMap(const LocalHDMap &hdmap);

    /**
     * @brief 获取解析结果
     */
    const HDMapElemInfo &GetParsedData() const noexcept { return hdmap_; }

    /**
     * @brief 清空数据
     */
    void Clear() noexcept { hdmap_.Reset(); }

    // ===== 数据访问接口 =====

    // 获取前方车道线
    const std::vector<HdLaneLine> &GetFutureLaneMarkings() const noexcept { return hdmap_.future_lane_markings; }

    // 获取后方车道线
    const std::vector<HdLaneLine> &GetPastLaneMarkings() const noexcept { return hdmap_.past_lane_markings; }

    // 获取前方中心线
    const std::vector<HdCenterline> &GetFutureCenterlines() const noexcept { return hdmap_.future_centerlines; }

    // 获取后方中心线
    const std::vector<HdCenterline> &GetPastCenterlines() const noexcept { return hdmap_.past_centerlines; }

    // 获取停止线
    const std::vector<HdStopLine> &GetStopLines() const noexcept { return hdmap_.stop_lines; }

    // 获取附近道路
    const std::vector<HdMapRoad> &GetNearbyRoads() const noexcept { return hdmap_.nearby_roads; }

    // 获取前方停止线ID
    const std::vector<int32_t> &GetFutureStopLineIds() const noexcept { return hdmap_.future_stop_line_ids; }

    // 获取后方停止线ID
    const std::vector<int32_t> &GetPastStopLineIds() const noexcept { return hdmap_.past_stop_line_ids; }

    // 获取前方可通行区域
    const std::vector<HdPoint2dEx> &GetFrontTraversableArea() const noexcept { return hdmap_.front_traversable_area; }

    // 获取后方可通行区域
    const std::vector<HdPoint2dEx> &GetRearTraversableArea() const noexcept { return hdmap_.rear_traversable_area; }

    // ===== 状态查询接口 =====
    // 检查数据有效性
    inline bool IsValid() const noexcept { return hdmap_.timestamp > 0.0; }
    // 获取时间戳
    double GetTimestamp() const noexcept { return hdmap_.timestamp; }
    // 获取header
    const xsproto::base::Header &GetHDMapHeader() const noexcept { return hdmap_.header; }
    // 静态函数
    // static const char *TurnTypeToString(LaneTurnType turn_type);

    // 检查是否有有效数据
    inline bool HasValidData() const noexcept { return hdmap_.timestamp > 0.0; }

    // ===== Ego车道速度管理 =====
    // 设置ego所在车道的速度(单位: m/s)
    void SetEgoLaneSpeed(float speed_mps) noexcept { hdmap_.ego_lane_speed = speed_mps; }
    // 获取ego所在车道的速度(单位: m/s)
    float GetEgoLaneSpeed() const noexcept { return hdmap_.ego_lane_speed; }

private:
    // 内部解析函数
    bool ParseLaneMarkings(const google::protobuf::RepeatedPtrField<MapLaneMarking> &proto_markings,
                           std::vector<HdLaneLine> &markings);

    bool ParseCenterlines(const google::protobuf::RepeatedPtrField<MapCenterline> &proto_centerlines,
                          std::vector<HdCenterline> &centerlines);

    bool ParseStopLines(const google::protobuf::RepeatedPtrField<MapStopLine> &proto_stop_lines);

    bool ParseNearbyRoads(const google::protobuf::RepeatedPtrField<MapRoad> &proto_roads,
                          std::vector<HdMapRoad> &roads);

    static const char *GetTurnTypeStringFast(LaneTurnType turn_type);

    // 构建可通行区域
    void BuildTraversableAreas() noexcept;

private:
    HDMapElemInfo hdmap_;
};

// 类型别名
using HdmapManagerPtr = std::unique_ptr<HdmapManager>;
using HdmapManagerSharedPtr = std::shared_ptr<HdmapManager>;

#endif // HDMAP_MGR_HPP
