#ifndef TRAFFIC_LIGHT_MGR_HPP
#define TRAFFIC_LIGHT_MGR_HPP

#include <chrono>
#include <map>
#include <memory>

#include <utility>
#include <vector>
#include "perception/traffic_light_info.pb.h"

// 红绿灯的类型
enum class LightType : uint8_t {
    LIGHT_TYPE_NONE = 0, // 未定义(默认)，不使用
    LIGHT_TYPE_LEFT = 1, // 左转灯
    LIGHT_TYPE_FORWARD = 2, // 直行灯
    LIGHT_TYPE_RIGHT = 3, // 右转灯
    LIGHT_TYPE_UTURN = 4 // 调头灯
};

// 灯的颜色状态: 0-无法判定，1-红色，2-黄色，3-绿色，4-黑色:
//  (与proto中的forward_type, left_type, right_type, uturn_type一致)
enum class LightColorState : uint8_t {
    LIGHT_NONE = 0,
    LIGHT_RED = 1,
    LIGHT_YELLOW = 2,
    LIGHT_GREEN = 3,
    LIGHT_BLACK = 4,
};

// 灯的闪烁状态: 0-无闪烁，1-绿转黄（或红）闪烁，2-黄灯长闪烁: 与proto中的flash_state一致
enum class LightFlashState : uint8_t {
    FLASH_NONE = 0,
    FLASH_GREEN_TRANSITION = 1, // 绿转黄（或红）闪烁
    FLASH_YELLOW_LONG = 2 // 黄灯长闪烁
};

// 灯状态的时序结构 - 时间维度
struct LightStateInfo {
    double timestamp;
    LightType light_type;
    LightColorState light_color;
    LightFlashState light_flash;

    LightStateInfo(double ts, LightType type, LightColorState color, LightFlashState flash) noexcept : timestamp(ts),
        light_type(type), light_color(color), light_flash(flash) {
    }
};

// 红绿灯状态结构 - 极致内存优化（40字节 -> 16字节）
struct TrafficLightFrame {
    double timestamp; // 时间戳（秒）- 8字节

    // 使用位域压缩存储 - 8字节总计
    union {
        uint64_t packed_data; // 打包存储所有状态
        struct {
            uint64_t forward_type: 4; // 0-15: 存储 0-3 状态
            uint64_t left_type: 4; // 0-15
            uint64_t right_type: 4; // 0-15
            uint64_t uturn_type: 4; // 0-15
            uint64_t forward_flash: 4; // 0-15: 存储 0-2 闪烁状态
            uint64_t left_flash: 4; // 0-15
            uint64_t right_flash: 4; // 0-15
            uint64_t uturn_flash: 4; // 0-15
            uint64_t reserved: 32; // 保留位
        };
    };

    // 默认构造
    TrafficLightFrame() noexcept : timestamp(0.0), packed_data(0) {
    }

    // 参数构造: 打包存储
    TrafficLightFrame(double ts, LightColorState fw, LightColorState lt, LightColorState rt, LightColorState ut,
                      LightFlashState fw_flash, LightFlashState lt_flash, LightFlashState rt_flash,
                      LightFlashState ut_flash) noexcept : timestamp(ts), packed_data(0) {
        forward_type = static_cast<uint32_t>(fw);
        left_type = static_cast<uint32_t>(lt);
        right_type = static_cast<uint32_t>(rt);
        uturn_type = static_cast<uint32_t>(ut);
        forward_flash = static_cast<uint32_t>(fw_flash);
        left_flash = static_cast<uint32_t>(lt_flash);
        right_flash = static_cast<uint32_t>(rt_flash);
        uturn_flash = static_cast<uint32_t>(ut_flash);
    }

    // 检查是否有检测结果
    inline bool HasTrafficLightState() const noexcept {
        // 检查前16位（4个灯的状态）是否有非零值
        return (packed_data & 0xFFFF) != 0;
    }

    // 获取指定灯类型的状态
    inline LightColorState GetLightState(LightType light_type) const noexcept {
        // 处理 LIGHT_TYPE_NONE 情况
        if (light_type == LightType::LIGHT_TYPE_NONE) {
            return LightColorState::LIGHT_NONE;
        }

        const uint64_t shift = static_cast<uint64_t>(light_type) << 2; // * 4
        return static_cast<LightColorState>((packed_data >> shift) & 0xF);
    }

    // 获取指定灯闪烁状态
    inline LightFlashState GetLightFlash(LightType light_type) const noexcept {
        // 处理 LIGHT_TYPE_NONE 的情况
        if (light_type == LightType::LIGHT_TYPE_NONE) {
            return LightFlashState::FLASH_NONE;
        }

        const uint64_t shift = 16 + (static_cast<uint64_t>(light_type) << 2); // 16 + * 4
        return static_cast<LightFlashState>((packed_data >> shift) & 0xF);
    }

    // 一次性写入所有状态
    inline void SetAllStates(LightColorState fw, LightColorState lt, LightColorState rt, LightColorState ut,
                             LightFlashState fw_flash, LightFlashState lt_flash, LightFlashState rt_flash,
                             LightFlashState ut_flash) noexcept {
        packed_data = static_cast<uint64_t>(fw) | (static_cast<uint64_t>(lt) << 4) | (static_cast<uint64_t>(rt) << 8) |
                      (static_cast<uint64_t>(ut) << 12) | (static_cast<uint64_t>(fw_flash) << 16) |
                      (static_cast<uint64_t>(lt_flash) << 20) | (static_cast<uint64_t>(rt_flash) << 24) |
                      (static_cast<uint64_t>(ut_flash) << 28);
    }

    // 设置单个灯类型的状态和闪烁: 支持 LIGHT_TYPE_NONE
    inline void SetLightState(LightType light_type, LightColorState color_state, LightFlashState flash_state) noexcept {
        // LIGHT_TYPE_NONE 不需要存储
        if (light_type == LightType::LIGHT_TYPE_NONE) {
            return;
        }

        const uint64_t light_shift = static_cast<uint64_t>(light_type) << 2; // * 4
        const uint64_t flash_shift = 16 + light_shift;

        // 清除旧值
        const uint64_t light_mask = ~(0xFULL << light_shift);
        const uint64_t flash_mask = ~(0xFULL << flash_shift);
        packed_data &= (light_mask & flash_mask);

        // 设置新值
        packed_data |= (static_cast<uint64_t>(color_state) << light_shift);
        packed_data |= (static_cast<uint64_t>(flash_state) << flash_shift);
    }

    // 检查指定灯类型是否有效: 包括对 LIGHT_TYPE_NONE 的处理
    inline bool IsValidLightType(LightType light_type) const noexcept {
        return light_type >= LightType::LIGHT_TYPE_NONE && light_type <= LightType::LIGHT_TYPE_UTURN;
    }
};

// 红绿灯管理类
class TrafficLightManager {
private:
    // 清理过期数据: 范围删除
    void CleanExpiredData_(double cutoff_timestamp) noexcept;

public:
    TrafficLightManager() noexcept;

    ~TrafficLightManager() = default;

    // ===== 核心数据操作接口 =====
public:
    // 从protobuf消息解析状态帧
    void ParseTrafficLightMsg(const xsproto::perception::TrafficLightInfo &tl_msg);

    // ===== 1. 空间操作接口 =====

    // 检查指定时长内是否有检测结果
    bool HasDetectionInRange(double duration = 0.5) const noexcept;

    // 获取指定时间戳的完整状态: 指针版本
    const TrafficLightFrame *GetFrameByTimestamp(double timestamp) const noexcept;

    // 获取指定时间戳的完整状态: 引用版本
    bool GetFrameByTimestamp(double timestamp, TrafficLightFrame &state) const noexcept;

    // 获取最新的红绿灯状态: 指针版本
    const TrafficLightFrame *GetLatestFrame() const noexcept;

    // 获取最新的红绿灯状态: 引用版本
    bool GetLatestFrame(TrafficLightFrame &state) const noexcept;

    // ===== 2. 时间操作接口 =====

    // 获取指定灯类型的所有状态
    void GetLightStatesByType(LightType light_type, std::vector<LightStateInfo> &light_states) const noexcept;

    // 获取指定灯类型的最新状态
    bool GetLatestLightState(LightType light_type, uint32_t &light_state, int32_t &flash_state) const noexcept;

    bool GetLatestLightState(LightType light_type, LightColorState &light_state) const noexcept;

    // 时间线数据
    inline const std::map<double, TrafficLightFrame> &GetTimelineStates() const noexcept {
        return timeline_states_;
    }

    // 获取指定时间范围的数据迭代器
    std::pair<std::map<double, TrafficLightFrame>::const_iterator, std::map<double, TrafficLightFrame>::const_iterator>
    GetTimelineRange(double start_time, double end_time) const noexcept;

    // ===== 维护管理接口 =====
    // 设置最大缓存时间
    inline void SetCacheTimeLength(double time_length, int max_frame_num = 100) noexcept {
        max_cache_len_s_ = time_length;
        cleanup_threshold_ = max_frame_num;
    }

    // 清空所有缓存数据
    void Clear() noexcept;

    // 获取最早时间戳
    double GetEarliestTimestamp() const noexcept {
        return timeline_states_.empty() ? 0.0 : timeline_states_.begin()->first;
    }

    // 获取最新时间戳
    double GetLatestTimestamp() const noexcept {
        return timeline_states_.empty() ? 0.0 : timeline_states_.rbegin()->first;
    }

    // 是否有数据
    inline bool HasValidData() const noexcept { return !timeline_states_.empty(); }

private:
    // ===== 数据结构 =====
    // 主数据容器：时间戳 -> 红绿灯状态帧
    std::map<double, TrafficLightFrame> timeline_states_;

    // 高频缓存: 避免重复查找
    mutable TrafficLightFrame latest_frame_cache_;
    mutable double latest_cache_timestamp_ = -1.0;
    mutable bool cache_valid_ = false;

    // 配置参数
    double max_cache_len_s_ = 10.0; // 默认缓存10秒. 1秒有10个数据(检测频率10hz)
    size_t cleanup_threshold_ = 100; // 超过100条记录时触发清理
};

#endif // TRAFFIC_LIGHT_MGR_HPP
