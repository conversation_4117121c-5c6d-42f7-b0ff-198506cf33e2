#include "tl_mgr.hpp"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <iomanip>
#include <iostream>
#include <limits>

#include "params.hpp"

// TrafficLightManager构造函数
TrafficLightManager::TrafficLightManager() noexcept : latest_cache_timestamp_(-1.0), cache_valid_(false),
                                                      max_cache_len_s_(10.0), cleanup_threshold_(100) {
}

// 解析protobuf消息
void TrafficLightManager::ParseTrafficLightMsg(const xsproto::perception::TrafficLightInfo &tl_msg) {
    // 快速提取时间戳，减少函数调用
    const double timestamp = tl_msg.header().local_pose().timestamp();

    // 预检查：避免处理重复或过期的数据
    if (timestamp > latest_cache_timestamp_ + 0.5 || timestamp < latest_cache_timestamp_) [[unlikely]] {
        Clear(); // 重新回放的数据, 清空缓存
    }

    // 预先计算位域数据，减少访问次数
    // std::cout << "=== ParseTrafficLightMsg called, timestamp: " << std::fixed << std::setprecision(2) << timestamp <<
    // std::endl; std::cout << "--> tl_msg.has_traffic_light(): " << tl_msg.has_traffic_light() << std::endl;
    // if (!tl_msg.has_traffic_light()) [[unlikely]] {
    //     //std::cout << "--> NO traffic_light data, returning early ===" << std::endl;
    //     return;
    // }
    // std::cout << "=== traffic_light data EXISTS, continuing ===" << std::endl;

    const auto &tl = tl_msg.traffic_light();
    // 一次性构造位域数据，避免多次位运算
    uint64_t packed_states =
            static_cast<uint64_t>(tl.forward_type()) | (static_cast<uint64_t>(tl.left_type()) << 4) |
            (static_cast<uint64_t>(tl.right_type()) << 8) | (static_cast<uint64_t>(tl.uturn_type()) << 12) |
            (static_cast<uint64_t>(tl.forward_flash()) << 16) | (static_cast<uint64_t>(tl.left_flash()) << 20) |
            (static_cast<uint64_t>(tl.right_flash()) << 24) | (static_cast<uint64_t>(tl.uturn_flash()) << 28);

    // print traffic light info
    // std::cout << "new tl. timestamp: " << std::fixed << std::setprecision(2) << timestamp << std::endl;
    // // print forward_type/left_type/right_type/uturn_type
    // std::cout << "forward_type: " << tl.forward_type() << ", flash: " << tl.forward_flash() << std::endl;
    // std::cout << "left_type: " << tl.left_type() << ", flash: " << tl.left_flash() << std::endl;
    // std::cout << "right_type: " << tl.right_type() << ", flash: " << tl.right_flash() << std::endl;
    // std::cout << "uturn_type: " << tl.uturn_type() << ", flash: " << tl.uturn_flash() << std::endl;

    // 使用emplace原地构造. 类型：std::pair<iterator, bool>
    auto insert_data = timeline_states_.emplace(std::piecewise_construct, std::forward_as_tuple(timestamp),
                                                std::forward_as_tuple() // 默认构造TrafficLightFrame
    );

    // 插入成功时, 分别设置数据
    if (insert_data.second) [[likely]] {
        // 更新最新状态缓存
        latest_frame_cache_.timestamp = timestamp;
        latest_frame_cache_.packed_data = packed_states;
        latest_cache_timestamp_ = timestamp;
        cache_valid_ = true;

        // 原地构造的数据
        auto &tl_frame = insert_data.first->second;
        tl_frame.timestamp = timestamp;
        tl_frame.packed_data = packed_states;

        // 智能清理：延迟清理
        const size_t current_size = timeline_states_.size();
        if (current_size > cleanup_threshold_) [[unlikely]] {
            double cutoff_time = timestamp - max_cache_len_s_;
            CleanExpiredData_(cutoff_time); // 使用现有的清理函数
        }
    }
    // 如果insert_data.second为false，说明时间戳重复，直接忽略
}

// 检查指定时长内是否有检测结果
bool TrafficLightManager::HasDetectionInRange(double duration_s) const noexcept {
    if (timeline_states_.empty()) {
        return false;
    }

    // 获取最新时间戳
    double current_time = timeline_states_.rbegin()->first;
    double start_time = current_time - duration_s;

    // 检查缓存是否在时间范围内
    if (cache_valid_ && latest_cache_timestamp_ >= start_time) {
        if (latest_frame_cache_.HasTrafficLightState()) {
            return true;
        }
    }

    // 查找算法
    auto start_it = timeline_states_.lower_bound(start_time);
    auto end_it = timeline_states_.upper_bound(current_time);
    // 使用std::any_of
    return std::any_of(start_it, end_it, [](const auto &pair) { return pair.second.HasTrafficLightState(); });
}

// 获取指定时间戳的完整状态
const TrafficLightFrame *TrafficLightManager::GetFrameByTimestamp(double timestamp) const noexcept {
    if (timeline_states_.empty()) {
        return nullptr;
    }

    // 检查是否请求最新数据
    if (cache_valid_ && timestamp >= latest_cache_timestamp_) [[likely]] {
        return &latest_frame_cache_;
    }

    // 精确查找
    auto exact_it = timeline_states_.find(timestamp);
    if (exact_it != timeline_states_.end()) [[likely]] {
        return &exact_it->second;
    }

    // 最近邻查找
    auto lower_it = timeline_states_.lower_bound(timestamp);
    if (lower_it == timeline_states_.end()) {
        return &timeline_states_.rbegin()->second; // 超范围，返回最后一个
    }
    if (lower_it == timeline_states_.begin()) {
        return &lower_it->second; // 在范围前，返回第一个
    }

    // 最近的
    auto prev_it = std::prev(lower_it);
    if ((timestamp - prev_it->first) <= (lower_it->first - timestamp)) {
        return &prev_it->second;
    } else {
        return &lower_it->second;
    }
}

// 获取指定时间戳的完整状态: 引用版本
bool TrafficLightManager::GetFrameByTimestamp(double timestamp, TrafficLightFrame &state) const noexcept {
    if (timeline_states_.empty()) {
        return false;
    }

    // 快速路径：检查是否请求最新数据
    if (cache_valid_ && timestamp >= latest_cache_timestamp_) [[likely]] {
        state = latest_frame_cache_;
        return true;
    }

    // 精确查找
    auto exact_it = timeline_states_.find(timestamp);
    if (exact_it != timeline_states_.end()) [[likely]] {
        state = exact_it->second;
        return true;
    }

    // 最近邻查找
    auto lower_it = timeline_states_.lower_bound(timestamp);

    if (lower_it == timeline_states_.end()) {
        state = timeline_states_.rbegin()->second; // 返回最新数据
    } else if (lower_it == timeline_states_.begin()) {
        state = lower_it->second; // 返回最早数据
    } else {
        // 比较相邻两个时间戳，选择更近的返回
        auto prev_it = std::prev(lower_it);
        state = (timestamp - prev_it->first <= lower_it->first - timestamp) ? prev_it->second : lower_it->second;
    }

    return true;
}

// 获取最新的红绿灯状态: 指针版本
const TrafficLightFrame *TrafficLightManager::GetLatestFrame() const noexcept {
    if (timeline_states_.empty()) {
        return nullptr;
    }

    // 优先使用缓存，避免map查找
    if (cache_valid_) [[likely]] {
        return &latest_frame_cache_;
    }

    // 缓存失效时重建缓存
    const auto &latest_pair = *timeline_states_.rbegin();
    latest_frame_cache_ = latest_pair.second;
    latest_cache_timestamp_ = latest_pair.first;
    cache_valid_ = true;

    return &latest_frame_cache_;
}

// 获取最新的红绿灯状态
bool TrafficLightManager::GetLatestFrame(TrafficLightFrame &state) const noexcept {
    if (timeline_states_.empty()) {
        return false;
    }

    // 优先使用缓存
    if (cache_valid_) [[likely]] {
        state = latest_frame_cache_;
        return true;
    }

    // 缓存失效时重建缓存
    const auto &latest_pair = *timeline_states_.rbegin();
    latest_frame_cache_ = latest_pair.second;
    latest_cache_timestamp_ = latest_pair.first;
    cache_valid_ = true;

    state = latest_frame_cache_;
    return true;
}

void TrafficLightManager::GetLightStatesByType(LightType light_type,
                                               std::vector<LightStateInfo> &light_states) const noexcept {
    light_states.clear();

    if (timeline_states_.empty()) {
        return;
    }

    // 预分配
    light_states.reserve(timeline_states_.size());

    // 遍历所有数据: 提取指定灯类型的状态
    for (const auto &entry: timeline_states_) {
        const auto &frame = entry.second;

        // 获取指定灯类型的状态和闪烁状态
        LightColorState light_color = frame.GetLightState(light_type);
        LightFlashState flash_state = frame.GetLightFlash(light_type);
        light_states.emplace_back(frame.timestamp, light_type, light_color, flash_state);
    }
}

// 获取指定灯类型的最新状态
bool TrafficLightManager::GetLatestLightState(LightType light_type, uint32_t &light_state,
                                              int32_t &flash_state) const noexcept {
    if (timeline_states_.empty()) {
        return false;
    }

    // 使用缓存避免map查找
    const TrafficLightFrame *frame = nullptr;
    if (cache_valid_) [[likely]] {
        frame = &latest_frame_cache_;
    } else {
        // 重建缓存
        const auto &latest_pair = *timeline_states_.rbegin();
        latest_frame_cache_ = latest_pair.second;
        latest_cache_timestamp_ = latest_pair.first;
        cache_valid_ = true;
        frame = &latest_frame_cache_;
    }

    light_state = static_cast<uint32_t>(frame->GetLightState(light_type));
    flash_state = static_cast<int32_t>(frame->GetLightFlash(light_type));
    return true;
}

bool TrafficLightManager::GetLatestLightState(LightType light_type, LightColorState &light_state) const noexcept {
    uint32_t light_state_val = 0;
    int32_t flash_state_val = 0;
    if (GetLatestLightState(light_type, light_state_val, flash_state_val)) {
        light_state = static_cast<LightColorState>(light_state_val);
        return true;
    }
    return false;
}

// 清理过期数据
void TrafficLightManager::CleanExpiredData_(double cutoff_timestamp) noexcept {
    if (timeline_states_.empty()) {
        return;
    }

    // 检查缓存是否会被删除
    if (cache_valid_ && latest_cache_timestamp_ < cutoff_timestamp) {
        cache_valid_ = false;
    }

    // 高效删除
    auto cutoff_it = timeline_states_.lower_bound(cutoff_timestamp);
    timeline_states_.erase(timeline_states_.begin(), cutoff_it);
}

// 清空所有缓存数据
void TrafficLightManager::Clear() noexcept {
    timeline_states_.clear();
    cache_valid_ = false;
    latest_cache_timestamp_ = -1.0;
}

// 获取指定时间范围的数据迭代器
std::pair<std::map<double, TrafficLightFrame>::const_iterator, std::map<double, TrafficLightFrame>::const_iterator>
TrafficLightManager::GetTimelineRange(double start_time, double end_time) const noexcept {
    auto start_it = timeline_states_.lower_bound(start_time);
    auto end_it = timeline_states_.upper_bound(end_time);
    return std::make_pair(start_it, end_it);
}
