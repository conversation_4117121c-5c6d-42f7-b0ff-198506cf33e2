// task_mgr.hpp
// 任务管理类: 解析任务列表消息，并提供任务点信息
// 该类在主线程使用, 不涉及多线程
// coder: lhw
// date: 2025/08/06

#ifndef TASK_MGR_HPP_
#define TASK_MGR_HPP_

#include <opencv2/opencv.hpp>
#include "globalpath/task_list.pb.h"


enum class TaskTurnInfo {
    TASK_TURN_NONE = 0,
    TASK_TURN_LEFT = 1,
    TASK_TURN_FORWARD = 2,
    TASK_TURN_RIGHT = 3,
    TASK_TURN_UTURN = 4,
};

struct TaskPointInfo {
    TaskTurnInfo turn_info{TaskTurnInfo::TASK_TURN_NONE};
    cv::Point2f xy{};

    float angle{0.0f};
    float max_speed{0.0f};
    int centerline_id{-1};
};

class TaskManager {
    std::vector<TaskPointInfo> task_points_;
    TaskTurnInfo front_turn_info_{TaskTurnInfo::TASK_TURN_NONE};

public:
    TaskManager();
    ~TaskManager();

    void ParseTaskListMsg(const xsproto::globalpath::TaskList &task_list);
    const std::vector<TaskPointInfo> &GetTaskPoints() const;
    bool HasValidData() const noexcept { return !task_points_.empty(); }

    void SetFrontTurnInfo(const TaskTurnInfo &turn_info) noexcept { front_turn_info_ = turn_info; }
    TaskTurnInfo GetFrontTurnInfo() const noexcept { return front_turn_info_; }
};

#endif
