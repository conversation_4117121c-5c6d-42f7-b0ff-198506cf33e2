#include "hdmap_mgr.hpp"
#include <algorithm>
#include <chrono>
#include <iomanip>
#include <iostream>
#include "params.hpp"

// 主函数
bool HdmapManager::ParseLocalHDMap(const LocalHDMap &hdmap) {
    // std::cout << TimeString() << " [HDMapManager] got hdmap. timestamp: " << hdmap.header().local_pose().timestamp()
    //           << std::endl;
    // 清空之前的数据
    hdmap_.Reset();

    // 设置header
    hdmap_.header = hdmap.header();

    // 设置时间戳
    hdmap_.timestamp = hdmap.header().local_pose().timestamp();

    bool success = true;

    try {
        // 解析前方车道线
        if (!ParseLaneMarkings(hdmap.future_lanemarkings(), hdmap_.future_lane_markings)) {
            success = false;
        }

        // 解析后方车道线
        if (!ParseLaneMarkings(hdmap.past_lanemarkings(), hdmap_.past_lane_markings)) {
            success = false;
        }

        // 提取future_lane_markings的stop_line_id (保持插入顺序，自动去重)
        for (const auto &proto_lane_marking: hdmap.future_lanemarkings()) {
            // 从ext_points中提取stop_line_id
            for (const auto &ext_point: proto_lane_marking.ext_points()) {
                const int32_t stop_line_id = ext_point.stop_line_id();
                if (stop_line_id != 0) {
                    // 默认为0表示无停止线，跳过
                    // 去重：只有当不存在时才插入
                    if (std::find(hdmap_.future_stop_line_ids.begin(), hdmap_.future_stop_line_ids.end(),
                                  stop_line_id) == hdmap_.future_stop_line_ids.end()) {
                        hdmap_.future_stop_line_ids.emplace_back(stop_line_id);
                    }
                }
            }
        }

        // 提取past_lane_markings的stop_line_id (保持插入顺序，自动去重)
        for (const auto &proto_lane_marking: hdmap.past_lanemarkings()) {
            // 从ext_points中提取stop_line_id
            for (const auto &ext_point: proto_lane_marking.ext_points()) {
                const int32_t stop_line_id = ext_point.stop_line_id();
                if (stop_line_id != 0) {
                    // 默认为0表示无停止线，跳过
                    // 去重：只有当不存在时才插入
                    if (std::find(hdmap_.past_stop_line_ids.begin(), hdmap_.past_stop_line_ids.end(),
                                  stop_line_id) == hdmap_.past_stop_line_ids.end()) {
                        hdmap_.past_stop_line_ids.emplace_back(stop_line_id);
                    }
                }
            }
        }

        // 解析前方中心线
        if (!ParseCenterlines(hdmap.future_centerlines(), hdmap_.future_centerlines)) {
            success = false;
        }

        // 解析后方中心线
        if (!ParseCenterlines(hdmap.past_centerlines(), hdmap_.past_centerlines)) {
            success = false;
        }

        // 解析停止线
        if (!ParseStopLines(hdmap.stop_lines())) {
            success = false;
        }

        // 解析附近的道路数据
        if (!ParseNearbyRoads(hdmap.surrounding_roads(), hdmap_.nearby_roads)) {
            success = false;
        }
    } catch (const std::exception &e) {
        std::cout << "failed. what: " << e.what() << std::endl;
        success = false;
        // 失败时确保timestamp保持为0，表示无效状态
        hdmap_.timestamp = 0.0;
    }

    // 计算前方可通行区域和后方可通行区域
    if (success) {
        BuildTraversableAreas();
    }

    return success;
}

//==================== 工具函数 ====================
// 快速转向类型字符串映射
const char *HdmapManager::GetTurnTypeStringFast(LaneTurnType turn_type) {
    switch (turn_type) {
        case LaneTurnType::NO_TURN:
            return "no_turn";
        case LaneTurnType::JUNCTION_TURN_LEFT:
            return "left_type";
        case LaneTurnType::JUNCTION_TURN_GO_STRAIGHT:
            return "forward_type";
        case LaneTurnType::JUNCTION_TURN_RIGHT:
            return "right_type";
        case LaneTurnType::JUNCTION_TURN_U_TURN:
            return "uturn_type";
        case LaneTurnType::NORMAL_TURN_LEFT:
            return "normal_left";
        case LaneTurnType::NORMAL_TURN_RIGHT:
            return "normal_right";
        case LaneTurnType::NORMAL_TURN_U_TURN:
            return "normal_uturn";
        default:
            return "unknown_type";
    }
}

//==================== 解析函数 ====================
// 解析车道线
bool HdmapManager::ParseLaneMarkings(const google::protobuf::RepeatedPtrField<MapLaneMarking> &proto_markings,
                                     std::vector<HdLaneLine> &markings) {
    if (proto_markings.empty()) {
        return true; // 空数据算成功
    }

    // 预分配内存
    markings.reserve(proto_markings.size());

    int sequence_counter = 0;
    for (const auto &proto_marking: proto_markings) {
        HdLaneLine lane_line;

        // 基础属性
        lane_line.road_id = proto_marking.road_id();
        lane_line.lane_id = proto_marking.lane_id();
        lane_line.sequence_id = sequence_counter++;

        // print first 10 points of proto_marking.ext_points and proto_marking.ptx/pty
        // if (sequence_counter < 10) {
        //     std::cout << "proto_marking.ext_points_size(): " << proto_marking.ext_points_size() << std::endl;
        //     for (int i = 0; i < std::min(10, proto_marking.ext_points_size()); i++) {
        //         std::cout << i << ": (" << proto_marking.ext_points(i).x() << ", " << proto_marking.ext_points(i).y()
        //                 << ")" << std::endl;
        //     }
        //     std::cout << "proto_marking.ptx_size(): " << proto_marking.ptx_size() << std::endl;
        //     std::cout << "proto_marking.pty_size(): " << proto_marking.pty_size() << std::endl;
        //     for (int i = 0; i < std::min(10, proto_marking.ptx_size()); i++) {
        //         std::cout << i << ": (" << proto_marking.ptx(i) << ", " << proto_marking.pty(i) << ")" << std::endl;
        //     }
        // }

        if (proto_marking.ptx_size() > 0 && proto_marking.pty_size() > 0) {
            // 使用简单坐标数组（规划程序默认使用的方式）
            const auto size = std::min(proto_marking.ptx_size(), proto_marking.pty_size());
            lane_line.points.reserve(size);

            for (int i = 0; i < size; ++i) {
                // std::cout << "point: " << proto_marking.ptx(i) << ", " << proto_marking.pty(i) << std::endl;
                lane_line.points.emplace_back(proto_marking.ptx(i), proto_marking.pty(i),
                                              LaneTurnType::NO_TURN, // 默认转向类型
                                              0 // 默认停止线ID
                );
            }
        }
        // 提取点数据：使用带属性的点
        else if (proto_marking.ext_points_size() > 0) {
            // 使用 LaneMarkingPoint 提取完整属性
            lane_line.points.reserve(proto_marking.ext_points_size());

            for (const auto &point: proto_marking.ext_points()) {
                // print point
                // std::cout << "point: " << point.x() << ", " << point.y() << std::endl;
                lane_line.points.emplace_back(point); // 使用ExtPoint2D的构造函数
            }
        }

        // 只添加有效数据
        if (lane_line.IsValid()) {
            markings.emplace_back(std::move(lane_line));
        }
    }

    return true;
}

// 解析中心线
bool HdmapManager::ParseCenterlines(const google::protobuf::RepeatedPtrField<MapCenterline> &proto_centerlines,
                                    std::vector<HdCenterline> &centerlines) {
    if (proto_centerlines.empty()) {
        return true; // 空数据算成功
    }

    // 预分配内存
    centerlines.reserve(proto_centerlines.size());

    int sequence_counter = 0;
    for (const auto &proto_centerline: proto_centerlines) {
        HdCenterline centerline;

        // 设置基础属性
        centerline.sequence_id = sequence_counter++;
        centerline.overall_turn_type = proto_centerline.turn_type();
        centerline.turn_type_str = GetTurnTypeStringFast(centerline.overall_turn_type);

        // print first 10 points of proto_centerline.centerline_points and proto_centerline.ptx/pty
        // std::cout << "proto_centerline.centerline_points_size(): " << proto_centerline.centerline_points_size() <<
        // std::endl; for (int i = 0; i < std::min(10, proto_centerline.centerline_points_size()); i++) {
        //     std::cout << i << ": (" << proto_centerline.centerline_points(i).x() << ", " <<
        //     proto_centerline.centerline_points(i).y() << ")" << std::endl;
        // }

        // std::cout << "proto_centerline.ptx_size(): " << proto_centerline.ptx_size() << std::endl;
        // std::cout << "proto_centerline.pty_size(): " << proto_centerline.pty_size() << std::endl;
        // for(int i = 0; i < std::min(10, proto_centerline.ptx_size()); i++) {
        //     std::cout << i << ": (" << proto_centerline.ptx(i) << ", " << proto_centerline.pty(i) << ")" <<
        //     std::endl;
        // }

        // 提取点数据：优先使用带属性的中心线点 (与规划程序一致)
        if (proto_centerline.centerline_points_size() > 0) {
            centerline.points.reserve(proto_centerline.centerline_points_size());

            for (const auto &point: proto_centerline.centerline_points()) {
                // std::cout << "point: " << point.x() << ", " << point.y() << std::endl;
                centerline.points.emplace_back(point); // 使用ExtPoint2D的构造函数
            }
        } else if (proto_centerline.ptx_size() > 0 && proto_centerline.pty_size() > 0) {
            // 使用简单坐标数组（使用overall_turn_type）
            const auto size = std::min(proto_centerline.ptx_size(), proto_centerline.pty_size());
            centerline.points.reserve(size);

            for (int i = 0; i < size; ++i) {
                // std::cout << "point: " << proto_centerline.ptx(i) << ", " << proto_centerline.pty(i) << std::endl;
                centerline.points.emplace_back(proto_centerline.ptx(i), proto_centerline.pty(i),
                                               centerline.overall_turn_type, // 使用整体转向类型
                                               0 // 停止线ID默认为0
                );
            }
        }

        // print

        // 只添加有效数据
        if (centerline.IsValid()) {
            centerlines.emplace_back(std::move(centerline));
        }
    }

    return true;
}

bool HdmapManager::ParseStopLines(const google::protobuf::RepeatedPtrField<MapStopLine> &proto_stop_lines) {
    if (proto_stop_lines.empty()) {
        return true; // 空数据也算成功
    }

    // 预分配内存
    hdmap_.stop_lines.reserve(proto_stop_lines.size());

    // size_t index = 0;
    for (const auto &proto_stop_line: proto_stop_lines) {
        // 检查是否有足够的点数据
        if (proto_stop_line.ptx_size() < 2 || proto_stop_line.pty_size() < 2) {
            continue; // 跳过无效的停止线
        }

        // if (proto_stop_line.ptx_size() > 2) {
        //     std::cout << "[Warning, stopline] proto_stop_line.ptx_size() > 2: " << proto_stop_line.ptx_size()
        //               << std::endl;
        // }
        // // print start/end point of proto_stop_line
        // std::cout << proto_stop_line.id() << std::fixed << std::setprecision(2) << ": (" << proto_stop_line.ptx(0)
        //           << ", " << proto_stop_line.pty(0) << ")"
        //           << " -> (" << proto_stop_line.ptx(1) << ", " << proto_stop_line.pty(1) << ")" << std::endl;

        HdStopLine stop_line;

        // 设置基础属性
        stop_line.id = proto_stop_line.id();
        stop_line.line_type = proto_stop_line.line_type();

        // 设置起点和终点（使用前两个点）
        stop_line.start_point = HdPoint2d(proto_stop_line.ptx(0), proto_stop_line.pty(0));
        stop_line.end_point = HdPoint2d(proto_stop_line.ptx(1), proto_stop_line.pty(1));

        // 预计算几何属性
        stop_line.center_point = HdPoint2d((stop_line.start_point.x + stop_line.end_point.x) * 0.5,
                                           (stop_line.start_point.y + stop_line.end_point.y) * 0.5);

        stop_line.length = stop_line.start_point.DistanceTo(stop_line.end_point);

        stop_line.angle = std::atan2(stop_line.end_point.y - stop_line.start_point.y,
                                     stop_line.end_point.x - stop_line.start_point.x);

        // 只添加有效数据
        if (stop_line.IsValid()) {
            hdmap_.stop_lines.emplace_back(std::move(stop_line));
        }
    }

    return true;
}

// 解析附近道路数据
bool HdmapManager::ParseNearbyRoads(const google::protobuf::RepeatedPtrField<MapRoad> &proto_roads,
                                    std::vector<HdMapRoad> &roads) {
    if (proto_roads.empty()) {
        return true; // 空数据算成功
    }

    // 预分配内存 - 性能优化
    roads.clear();
    roads.reserve(proto_roads.size());

    try {
        for (const auto &proto_road: proto_roads) {
            HdMapRoad road_data;

            // 设置道路ID
            road_data.road_id = proto_road.road_id();

            // 预分配车道内存
            const int lanes_count = proto_road.lanes_size();
            if (lanes_count > 0) {
                road_data.lanes.reserve(lanes_count);

                for (const auto &proto_lane: proto_road.lanes()) {
                    HdMapLane lane_data;

                    // 设置车道ID
                    lane_data.lane_id = proto_lane.lane_index();

                    // 解析中心线数据
                    const auto &centerline = proto_lane.centerline();
                    if (centerline.ptx_size() > 0 && centerline.pty_size() > 0) {
                        const int center_points_count = std::min(centerline.ptx_size(), centerline.pty_size());
                        lane_data.center_pts.reserve(center_points_count);

                        for (int i = 0; i < center_points_count; ++i) {
                            lane_data.center_pts.emplace_back(centerline.ptx(i), centerline.pty(i));
                        }
                    }

                    // 解析左边界数据
                    const auto &left_boundary = proto_lane.left_boundary();
                    if (left_boundary.ptx_size() > 0 && left_boundary.pty_size() > 0) {
                        const int left_points_count = std::min(left_boundary.ptx_size(), left_boundary.pty_size());
                        lane_data.left_pts.reserve(left_points_count);

                        for (int i = 0; i < left_points_count; ++i) {
                            lane_data.left_pts.emplace_back(left_boundary.ptx(i), left_boundary.pty(i));
                        }
                    }

                    // 解析右边界数据
                    const auto &right_boundary = proto_lane.right_boundary();
                    if (right_boundary.ptx_size() > 0 && right_boundary.pty_size() > 0) {
                        const int right_points_count = std::min(right_boundary.ptx_size(), right_boundary.pty_size());
                        lane_data.right_pts.reserve(right_points_count);

                        for (int i = 0; i < right_points_count; ++i) {
                            lane_data.right_pts.emplace_back(right_boundary.ptx(i), right_boundary.pty(i));
                        }
                    }

                    // 添加有效的车道数据: 至少有一种线条数据
                    if (!lane_data.center_pts.empty() || !lane_data.left_pts.empty() || !lane_data.right_pts.empty()) {
                        road_data.lanes.emplace_back(std::move(lane_data));
                    }
                }
            }

            // 添加有效的道路数据: 至少有一个车道
            if (!road_data.lanes.empty()) {
                roads.emplace_back(std::move(road_data));
            }
        }

        return true;
    } catch (const std::exception &e) {
        std::cerr << "[HdmapManager] ParseNearbyRoads failed: " << e.what() << std::endl;
        return false;
    }
}

// 构建可通行区域
void HdmapManager::BuildTraversableAreas() noexcept {
    try {
        // ===== 构建前方可通行区域 =====
        hdmap_.front_traversable_area.clear();
        
        if (!hdmap_.future_lane_markings.empty()) {
            const auto& future_markings = hdmap_.future_lane_markings;
            
            // 预分配内存以提升性能：第一条线 + 最后一条线的倒序
            size_t estimated_size = 0;
            if (!future_markings.empty()) {
                estimated_size += future_markings.front().points.size();
            }
            if (future_markings.size() > 1) {
                estimated_size += future_markings.back().points.size();
            }
            hdmap_.front_traversable_area.reserve(estimated_size);
            
            // 添加第一条线的所有点
            if (!future_markings.front().points.empty()) {
                const auto& first_line = future_markings.front().points;
                hdmap_.front_traversable_area.insert(
                    hdmap_.front_traversable_area.end(),
                    first_line.begin(),
                    first_line.end()
                );
            }
            
            // 如果有多条线，添加最后一条线的倒序点
            if (future_markings.size() > 1 && !future_markings.back().points.empty()) {
                const auto& last_line = future_markings.back().points;
                hdmap_.front_traversable_area.insert(
                    hdmap_.front_traversable_area.end(),
                    last_line.rbegin(),
                    last_line.rend()
                );
            }
        }
        
        // ===== 构建后方可通行区域 =====
        hdmap_.rear_traversable_area.clear();
        
        if (!hdmap_.past_lane_markings.empty()) {
            const auto& past_markings = hdmap_.past_lane_markings;
            
            // 预分配内存以提升性能：第一条线 + 最后一条线的倒序
            size_t estimated_size = 0;
            if (!past_markings.empty()) {
                estimated_size += past_markings.front().points.size();
            }
            if (past_markings.size() > 1) {
                estimated_size += past_markings.back().points.size();
            }
            hdmap_.rear_traversable_area.reserve(estimated_size);
            
            // 添加第一条线的所有点
            if (!past_markings.front().points.empty()) {
                const auto& first_line = past_markings.front().points;
                hdmap_.rear_traversable_area.insert(
                    hdmap_.rear_traversable_area.end(),
                    first_line.begin(),
                    first_line.end()
                );
            }
            
            // 如果有多条线，添加最后一条线的倒序点
            if (past_markings.size() > 1 && !past_markings.back().points.empty()) {
                const auto& last_line = past_markings.back().points;
                hdmap_.rear_traversable_area.insert(
                    hdmap_.rear_traversable_area.end(),
                    last_line.rbegin(),
                    last_line.rend()
                );
            }
        }
        
    } catch (const std::exception& e) {
        // 异常情况下清空数据，确保状态一致
        hdmap_.front_traversable_area.clear();
        hdmap_.rear_traversable_area.clear();
        std::cerr << "[HdmapManager] BuildTraversableAreas failed: " << e.what() << std::endl;
    }
}
