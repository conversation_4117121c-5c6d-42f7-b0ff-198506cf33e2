#include "scene_mgr.hpp"
#include <algorithm>
#include <cmath>
#include <iostream>
#include <limits>
#include <numeric>
#include <vector>

// AgentState 从 PerceptionObject 构造(obj的数值为全局坐标)
AgentState::AgentState(const PerceptionObject &obj, const LocalPose &ego_pose, double timestamp) :
    timestamp(timestamp), agent_type(AgentType::UNKNOWN), flags(0) {
    // corners 现在是 std::array，无需 reserve

    // 基本几何属性
    center.x = obj.center().x();
    center.y = obj.center().y();
    width = obj.width();
    length = obj.length();
    heading_rad = obj.r_angle();

    // 运动属性
    linear_velocity = obj.linear_speed();
    velocity_heading_rad = obj.linear_speed_angle();

    float local_x = ego_pose.dr_x() - obj.center().x();
    float local_y = ego_pose.dr_y() - obj.center().y();

    // 计算相对自车距离
    distance_to_ego = std::sqrt(local_x * local_x + local_y * local_y);

    // 转换角点坐标系 - 直接访问数组元素，零拷贝
    if (obj.corner().x_size() >= 4 && obj.corner().y_size() >= 4) {
        for (int i = 0; i < 4; ++i) {
            corners[i].x = obj.corner().x(i);
            corners[i].y = obj.corner().y(i);
        }
    } else {
        // 根据中心点、长宽、朝向计算
        float half_length = length * 0.5f;
        float half_width = width * 0.5f;
        float cos_heading = std::cos(heading_rad);
        float sin_heading = std::sin(heading_rad);

        // 左后、右后、右前、左前 - 直接赋值，更高效
        corners[0] = {center.x - half_length * cos_heading - half_width * sin_heading,
                      center.y - half_length * sin_heading + half_width * cos_heading};
        corners[1] = {center.x - half_length * cos_heading + half_width * sin_heading,
                      center.y - half_length * sin_heading - half_width * cos_heading};
        corners[2] = {center.x + half_length * cos_heading + half_width * sin_heading,
                      center.y + half_length * sin_heading - half_width * cos_heading};
        corners[3] = {center.x + half_length * cos_heading - half_width * sin_heading,
                      center.y + half_length * sin_heading + half_width * cos_heading};
    }

    agent_type = GetAgentType(obj.obj_type(), obj.obj_subtype());
}

// 将type从PerceptionObject转成AgentType
AgentType AgentState::GetAgentType(const ObjType &type, const ObjSubType &sub_type) {
    switch (type) {
        case OBJ_TYPE_VEHICLE:
            if (sub_type == OBJ_ST_TRICYCLE) {
                return AgentType::CYCLIST;
            }
            return AgentType::VEHICLE;

        case OBJ_TYPE_PERSON:
            // 根据子类型进一步细分
            switch (sub_type) {
                case OBJ_ST_MOTORLIST: // 摩托车骑行者
                case OBJ_ST_CYCLIST: // 自行车骑行者
                    return AgentType::CYCLIST;
                case OBJ_ST_PEDESTRIAN: // 行人
                case OBJ_ST_ADULT: // 成年人
                case OBJ_ST_CHILD: // 儿童
                case OBJ_ST_POLICEMAN: // 警察
                    return AgentType::PEDESTRIAN;
                default:
                    return AgentType::PEDESTRIAN; // 默认为行人
            }

        case OBJ_TYPE_STATIC: // 静态目标
        case OBJ_TYPE_DRIVABLE_AREA: // 可通行区域
        case OBJ_TYPE_LANE_LINE: // 车道线
        case OBJ_TYPE_UNKNOWN: // 未知类型
        default:
            return AgentType::UNKNOWN;
    }
}

// 添加最新的感知结果(timestamp一定大于当前缓存中的最新时间戳) - 高性能优化版
void SceneManager::ParsePerceptionInfo(const PerceptionObjectInfo &perception_info) {


    // 一次性提取所有需要的数据
    const auto &header = perception_info.header();
    const auto &ego_pose = header.local_pose();
    const Timestamp timestamp = ego_pose.timestamp();
    const int obj_count = perception_info.obs_objs_size();

    if (timestamp > GetLatestTimestamp() + 0.5 || timestamp < GetLatestTimestamp()) [[unlikely]] {
        // 时间间隔过大, 说明为重新回放的数据: 清空缓存
        ClearCache_();
    } else [[likely]] {
        // 清理过时数据
        CleanExpiredData_(timestamp - max_cache_time_seconds);
    }

    // 创建SceneFrame，预分配容器
    auto scene_frame = std::make_shared<SceneFrame>(header);
    scene_frame->agents.reserve(static_cast<size_t>(obj_count));

    // 批量处理所有感知目标 - 减少查找次数
    for (int i = 0; i < obj_count; ++i) {
        const auto &obj = perception_info.obs_objs(i);
        const AgentID agent_id = obj.track_id();

        // 使用 make_shared 一次性分配，减少内存碎片
        auto state = std::make_shared<AgentState>(obj, ego_pose, timestamp);

        // 在两个容器中共享同一个智能指针，避免重复存储
        agent_tracks_[agent_id].emplace(timestamp, state);
        scene_frame->agents.emplace(agent_id, state);
    }

    // 解析静态障碍物
    for (const auto &obstacle: perception_info.obstacle_areas()) {
        std::vector<cv::Point2f> obstacle_points;

        // 从Area2D中提取点坐标
        const auto &area = obstacle.area();
        const int point_count = area.x_size();

        obstacle_points.reserve(point_count);
        if (point_count > 0 && point_count == area.y_size()) {
            // 将坐标点添加到列表中
            for (int i = 0; i < point_count; ++i) {
                obstacle_points.emplace_back(area.x(i), area.y(i));
            }
        }

        // 只有当障碍物包含有效点时才添加到场景帧中
        if (!obstacle_points.empty()) {
            // 计算中心点：使用max/min x,y的中间值
            float min_x = obstacle_points[0].x;
            float max_x = obstacle_points[0].x;
            float min_y = obstacle_points[0].y;
            float max_y = obstacle_points[0].y;
            
            for (const auto& point : obstacle_points) {
                min_x = std::min(min_x, point.x);
                max_x = std::max(max_x, point.x);
                min_y = std::min(min_y, point.y);
                max_y = std::max(max_y, point.y);
            }
            
            cv::Point2f center((min_x + max_x) * 0.5f, (min_y + max_y) * 0.5f);
            
            // 创建StaticContour并添加到场景帧中
            StaticContour static_contour;
            static_contour.contour = std::move(obstacle_points);
            static_contour.center = center;
            scene_frame->static_contours.emplace_back(std::move(static_contour));
        }
    }


    // 使用 emplace 直接构造，避免中间拷贝
    scene_frames_.emplace(timestamp, std::move(scene_frame));
}

// 添加单个agent状态
void SceneManager::AddAgentState(const Header &header, AgentID agent_id, AgentStatePtr state) {


    if (!state)
        return;

    Timestamp timestamp = state->timestamp;

    // 添加到Agent历史
    agent_tracks_[agent_id].emplace(timestamp, state);

    // 添加到场景帧
    auto scene_it = scene_frames_.find(timestamp);
    if (scene_it == scene_frames_.end()) {
        auto scene = std::make_shared<SceneFrame>(header);
        scene->agents.emplace(agent_id, state);
        scene_frames_.emplace(timestamp, scene);
    } else {
        scene_it->second->agents[agent_id] = state;
    }
}

// 获取缓存统计信息
SceneManager::StatInfo SceneManager::GetStatInfo() const {
    StatInfo stats;

    stats.total_agents = agent_tracks_.size();
    stats.total_scenes = scene_frames_.size();
    stats.total_states = 0;
    stats.total_static_obstacles = 0;

    // 统计总状态数量
    for (const auto &[agent_id, history]: agent_tracks_) {
        stats.total_states += history.size();
    }

    // 统计静态障碍物总数量
    for (const auto &[timestamp, scene_frame]: scene_frames_) {
        stats.total_static_obstacles += scene_frame->static_contours.size();
    }

    // 获取时间范围
    if (!scene_frames_.empty()) {
        stats.earliest_timestamp = scene_frames_.begin()->first;
        stats.latest_timestamp = scene_frames_.rbegin()->first;
    } else {
        stats.earliest_timestamp = 0.0;
        stats.latest_timestamp = 0.0;
    }

    return stats;
}

// 获取agent的所有历史状态
const std::map<Timestamp, AgentStatePtr> *SceneManager::GetAgentHistory(AgentID agent_id) const noexcept {


    auto it = agent_tracks_.find(agent_id);
    return (it != agent_tracks_.end()) ? &it->second : nullptr;
}

// 获取agent在指定时间的状态
AgentStatePtr SceneManager::GetAgentState(AgentID agent_id, Timestamp timestamp) const noexcept {


    auto it = agent_tracks_.find(agent_id);
    if (it != agent_tracks_.end()) [[likely]] {
        auto jt = it->second.find(timestamp);
        return jt != it->second.end() ? jt->second : nullptr;
    }
    return nullptr;
}

// 获取agent的最新状态
AgentStatePtr SceneManager::GetAgentLatestState(AgentID agent_id) const noexcept {


    auto it = agent_tracks_.find(agent_id);
    if (it != agent_tracks_.end() && !it->second.empty()) [[likely]] {
        return it->second.rbegin()->second; // map最后一个元素(最大timestamp)
    }
    return nullptr;
}

// 获取agent在指定时间范围内的所有状态
void SceneManager::GetAgentStatesInRange(AgentID agent_id, Timestamp start_time, Timestamp end_time,
                                         std::vector<AgentStatePtr> &results) const noexcept {


    results.clear();
    auto it = agent_tracks_.find(agent_id);
    if (it != agent_tracks_.end()) [[likely]] {
        auto start_it = it->second.lower_bound(start_time);
        auto end_it = it->second.upper_bound(end_time);
        results.reserve(std::distance(start_it, end_it));
        for (auto iter = start_it; iter != end_it; ++iter) {
            results.push_back(iter->second);
        }
    }
}

// 移除agent的指定时间状态
bool SceneManager::RemoveAgentState(AgentID agent_id, Timestamp timestamp) {


    auto it = agent_tracks_.find(agent_id);
    if (it != agent_tracks_.end()) {
        size_t removed = it->second.erase(timestamp);
        if (it->second.empty()) {
            agent_tracks_.erase(it);
        }
        return removed > 0;
    }
    return false;
}

// 移除agent的所有历史数据
bool SceneManager::RemoveAgent(AgentID agent_id) { return agent_tracks_.erase(agent_id) > 0; }

// 获取所有已知的agent ID
void SceneManager::GetAllAgentIDs(std::vector<AgentID> &ids) const noexcept {


    ids.clear();
    ids.reserve(agent_tracks_.size());
    for (const auto &[id, _]: agent_tracks_) {
        ids.push_back(id);
    }
}

// 获取agent数量
size_t SceneManager::GetAgentCount() const noexcept { return agent_tracks_.size(); }

// 获取指定时刻的场景帧 - 零拷贝版本
const SceneFrame *SceneManager::GetSceneFrame(Timestamp timestamp) const noexcept {
    auto it = scene_frames_.find(timestamp);
    return it != scene_frames_.end() ? it->second.get() : nullptr;
}

// 获取指定时刻的场景帧 - shared_ptr版本（兼容性）
std::shared_ptr<SceneFrame> SceneManager::GetSceneFrameShared(Timestamp timestamp) const noexcept {
    auto it = scene_frames_.find(timestamp);
    return it != scene_frames_.end() ? it->second : nullptr;
}

// 获取指定时刻的所有agent状态
void SceneManager::GetSceneAgents(Timestamp timestamp,
                                  std::unordered_map<AgentID, AgentStatePtr> &result) const noexcept {


    auto it = scene_frames_.find(timestamp);
    if (it != scene_frames_.end()) [[likely]] {
        result = it->second->agents;
    } else {
        result.clear();
    }
}

// 获取指定时间范围内的所有时刻
void SceneManager::GetTimestampsInRange(Timestamp start_time, Timestamp end_time,
                                        std::vector<Timestamp> &timestamps) const noexcept {


    timestamps.clear();
    auto start_it = scene_frames_.lower_bound(start_time);
    auto end_it = scene_frames_.upper_bound(end_time);
    timestamps.reserve(std::distance(start_it, end_it));
    for (auto iter = start_it; iter != end_it; ++iter) {
        timestamps.push_back(iter->first);
    }
}

// 获取最新的场景帧 - 零拷贝版本
const SceneFrame *SceneManager::GetLatestSceneFrame() const noexcept {
    return scene_frames_.empty() ? nullptr : scene_frames_.rbegin()->second.get();
}

// 获取最新的场景帧 - shared_ptr版本（兼容性）
std::shared_ptr<SceneFrame> SceneManager::GetLatestSceneFrameShared() const noexcept {
    return scene_frames_.empty() ? nullptr : scene_frames_.rbegin()->second;
}

// 获取最新场景帧的Header
const Header *SceneManager::GetLatestSceneFrameHeader() const noexcept {


    return scene_frames_.empty() ? nullptr : &(scene_frames_.rbegin()->second->header_);
}

// 检索某个id在所有时间帧中的状态
void SceneManager::GetAgentStatesOverTime(AgentID agent_id, std::vector<AgentStatePtr> &results) const noexcept {


    results.clear();
    results.reserve(scene_frames_.size()); // 预分配最大可能容量
    for (const auto &[ts, scene]: scene_frames_) {
        auto state = scene->GetAgent(agent_id);
        if (state) [[likely]]
            results.push_back(state);
    }
}


// 清空所有数据
void SceneManager::Clear() { ClearCache_(); }

inline void SceneManager::ClearCache_() noexcept {
    agent_tracks_.clear();
    scene_frames_.clear();
}

// 清理过期数据(早于cutoff_timestamp)
void SceneManager::CleanExpiredData_(Timestamp cutoff_timestamp) {
    // 清理场景帧
    auto scene_it = scene_frames_.lower_bound(cutoff_timestamp);
    scene_frames_.erase(scene_frames_.begin(), scene_it);

    // 清理Agent历史数据
    for (auto agent_it = agent_tracks_.begin(); agent_it != agent_tracks_.end();) {
        auto &history = agent_it->second;

        // 清理该Agent早于指定时间的状态
        auto time_it = history.lower_bound(cutoff_timestamp);
        history.erase(history.begin(), time_it);

        // 如果该Agent没有剩余历史，移除整个Agent
        if (history.empty()) {
            agent_it = agent_tracks_.erase(agent_it);
        } else {
            ++agent_it;
        }
    }
}

bool SceneManager::HasValidData() const noexcept { return !scene_frames_.empty(); }

// 获取指定agent历史的迭代器范围 - 零拷贝
std::pair<std::map<Timestamp, AgentStatePtr>::const_iterator, std::map<Timestamp, AgentStatePtr>::const_iterator>
SceneManager::GetAgentHistoryRange(AgentID agent_id, Timestamp start_time, Timestamp end_time) const noexcept {
    auto agent_it = agent_tracks_.find(agent_id);
    if (agent_it != agent_tracks_.end()) {
        auto start_it = agent_it->second.lower_bound(start_time);
        auto end_it = agent_it->second.upper_bound(end_time);
        return std::make_pair(start_it, end_it);
    }
    // 返回空范围
    static const std::map<Timestamp, AgentStatePtr> empty_map;
    return std::make_pair(empty_map.end(), empty_map.end());
}

// 获取指定时间范围的场景帧迭代器 - 零拷贝
std::pair<std::map<Timestamp, std::shared_ptr<SceneFrame>>::const_iterator,
          std::map<Timestamp, std::shared_ptr<SceneFrame>>::const_iterator>
SceneManager::GetSceneFrameRange(Timestamp start_time, Timestamp end_time) const noexcept {
    auto start_it = scene_frames_.lower_bound(start_time);
    auto end_it = scene_frames_.upper_bound(end_time);
    return std::make_pair(start_it, end_it);
}

//返回指定时间范围的所有场景帧
std::vector<std::shared_ptr<SceneFrame>> SceneManager::GetSceneFrames(Timestamp start_time, Timestamp end_time) const noexcept {
    std::vector<std::shared_ptr<SceneFrame>> frames;
    auto start_it = scene_frames_.lower_bound(start_time);
    auto end_it = scene_frames_.upper_bound(end_time);
    frames.reserve(std::distance(start_it, end_it));
    for (auto iter = start_it; iter != end_it; ++iter) {
        frames.push_back(iter->second);
    }
    return frames;
}