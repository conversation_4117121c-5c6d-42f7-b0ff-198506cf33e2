#include "trigger_param.h"
#include <fstream>
#include <iostream>
#include <map>

using json = nlohmann::json;

// 键名到触发器类型的映射
static const std::map<std::string, TriggerType> KEY_TO_TYPE = {
    {"traffic_light_missing_in_range", TriggerType::TRAFFIC_LIGHT_MISSING_IN_RANGE},
    {"traffic_light_detected_out_of_range", TriggerType::TRAFFIC_LIGHT_DETECTED_OUT_OF_RANGE},
    {"traffic_light_illogical_state_change", TriggerType::TRAFFIC_LIGHT_ILLOGICAL_STATE_CHANGE},
    {"traffic_light_violate_traffic_flow", TriggerType::TRAFFIC_LIGHT_VIOLATE_TRAFFIC_FLOW},
    {"object_detection_instability", TriggerType::OBJECT_DETECTION_INSTABILITY},
    {"obstacle_detection_sudden_appearance", TriggerType::OBSTACLE_DETECTION_SUDDEN_APPEARANCE}
};

// 从JSON配置创建启用的触发器参数列表
TriggerParamList TriggerParamFactory::LoadConfig() {
    const std::string& config_file = param::_CONFIG_FILE_;
    TriggerParamList triggers;
    
    try {
        // 读取JSON配置文件
        std::ifstream file(config_file);
        if (!file.is_open()) {
            std::cerr << "Failed to open config file: " << config_file << std::endl;
            return triggers;
        }
        
        json config;
        file >> config;
        file.close();
        
        // 遍历所有配置项
        for (const auto& [key, value] : config.items()) {
            // // 检查是否启用
            // if (!value.contains("enabled") || !value["enabled"].get<bool>()) {
            //     continue;  // 跳过未启用的触发器
            // }
            
            // 根据键名确定触发器类型
            auto type_iter = KEY_TO_TYPE.find(key);
            if (type_iter == KEY_TO_TYPE.end()) {
                std::cerr << "Unknown trigger type: " << key << std::endl;
                continue;
            }
            
            // 创建触发器实例
            auto trigger = CreateTrigger(type_iter->second);
            if (!trigger) {
                std::cerr << "Failed to create trigger for type: " << key << std::endl;
                continue;
            }
            
            // 填充基础参数
            FillBaseParams(trigger.get(), value);
            
            // 填充特定参数
            FillSpecificParams(trigger.get(), value);
            
            // 添加到列表
            triggers.push_back(std::move(trigger));
        }
        
        std::cout << "Loaded " << triggers.size() << " enabled triggers from " << config_file << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading triggers: " << e.what() << std::endl;
    }
    
    return triggers;
}

// 根据类型创建具体的触发器参数
TriggerParamPtr TriggerParamFactory::CreateTrigger(TriggerType type) {
    switch (type) {
        case TriggerType::TRAFFIC_LIGHT_MISSING_IN_RANGE:
            return std::make_unique<TrafficLightMissingInRange>();
            
        case TriggerType::TRAFFIC_LIGHT_DETECTED_OUT_OF_RANGE:
            return std::make_unique<TrafficLightDetectedOutOfRange>();
            
        case TriggerType::TRAFFIC_LIGHT_ILLOGICAL_STATE_CHANGE:
            return std::make_unique<TrafficLightIllogicalStateChange>();
            
        case TriggerType::TRAFFIC_LIGHT_VIOLATE_TRAFFIC_FLOW:
            return std::make_unique<TrafficLightViolateTrafficFlow>();
            
        case TriggerType::OBJECT_DETECTION_INSTABILITY:
            return std::make_unique<ObjectDetectionInstability>();
            
        case TriggerType::OBSTACLE_DETECTION_SUDDEN_APPEARANCE:
            return std::make_unique<ObstacleDetectionSuddenAppearance>();
            
        default:
            return nullptr;
    }
}

// 从JSON填充基础参数
void TriggerParamFactory::FillBaseParams(BaseTriggerParam* param, const json& config) {
    if (!param) return;
    
    // 填充基础字段
    if (config.contains("enabled")) {
        param->enabled = config["enabled"].get<bool>();
    }
    
    if (config.contains("duration")) {
        param->duration = config["duration"].get<int>();
    }
    
    // 填充TaskParam
    FillTaskParam(param->task_param, config);
}

// 从JSON填充TaskParam
void TriggerParamFactory::FillTaskParam(TaskParam& task_param, const json& config) {
    if (config.contains("task_name")) {
        task_param.task_name = config["task_name"].get<std::string>();
    }
    
    if (config.contains("duration")) {
        task_param.duration = config["duration"].get<int>();
    }
    
    if (config.contains("data_sources") && config["data_sources"].is_array()) {
        task_param.data_sources.clear();
        for (const auto& source : config["data_sources"]) {
            task_param.data_sources.push_back(source.get<std::string>());
        }
    }
    
    if (config.contains("task_source")) {
        task_param.task_source = config["task_source"].get<std::string>();
    }
    
    if (config.contains("collect_scene")) {
        task_param.collect_scene = config["collect_scene"].get<std::string>();
    }
    
    if (config.contains("collect_reason")) {
        task_param.collect_reason = config["collect_reason"].get<std::string>();
    }
}

// 填充特定参数（根据不同类型填充专有参数）
void TriggerParamFactory::FillSpecificParams(BaseTriggerParam* param, const json& config) {
    if (!param) return;
    
    switch (param->type) {
        case TriggerType::TRAFFIC_LIGHT_MISSING_IN_RANGE: {
            auto* specific = param->as<TrafficLightMissingInRange>();
            if (config.contains("detection_range_threshold")) {
                specific->detection_range_threshold = config["detection_range_threshold"].get<int>();
            }
            break;
        }
        
        case TriggerType::TRAFFIC_LIGHT_DETECTED_OUT_OF_RANGE: {
            auto* specific = param->as<TrafficLightDetectedOutOfRange>();
            if (config.contains("false_detection_distance_threshold")) {
                specific->false_detection_distance_threshold = config["false_detection_distance_threshold"].get<int>();
            }
            break;
        }
        
        case TriggerType::TRAFFIC_LIGHT_ILLOGICAL_STATE_CHANGE: {
            auto* specific = param->as<TrafficLightIllogicalStateChange>();
            if (config.contains("distance_range_min")) {
                specific->distance_range_min = config["distance_range_min"].get<int>();
            }
            if (config.contains("distance_range_max")) {
                specific->distance_range_max = config["distance_range_max"].get<int>();
            }
            if (config.contains("jump_patterns") && config["jump_patterns"].is_array()) {
                specific->jump_patterns.clear();
                for (const auto& pattern : config["jump_patterns"]) {
                    specific->jump_patterns.push_back(pattern.get<std::string>());
                }
            }
            break;
        }
        
        case TriggerType::TRAFFIC_LIGHT_VIOLATE_TRAFFIC_FLOW: {
            auto* specific = param->as<TrafficLightViolateTrafficFlow>();
            if (config.contains("traffic_flow_average_speed_threshold")) {
                specific->traffic_flow_average_speed_threshold = config["traffic_flow_average_speed_threshold"].get<int>();
            }
            break;
        }
        
        case TriggerType::OBJECT_DETECTION_INSTABILITY: {
            auto* specific = param->as<ObjectDetectionInstability>();
            if (config.contains("detection_distance_threshold")) {
                specific->detection_distance_threshold = config["detection_distance_threshold"].get<int>();
            }
            break;
        }
        
        case TriggerType::OBSTACLE_DETECTION_SUDDEN_APPEARANCE: {
            auto* specific = param->as<ObstacleDetectionSuddenAppearance>();
            if (config.contains("obstacle_distance_threshold")) {
                specific->obstacle_distance_threshold = config["obstacle_distance_threshold"].get<int>();
            }
            break;
        }
    }
}

// 打印所有触发器的详细信息
void TriggerParamFactory::PrintAllTriggers(const TriggerParamList& triggers) {
    std::cout << "\n=== 触发器配置详情 ===" << std::endl;
    std::cout << "总共加载 " << triggers.size() << " 个触发器" << std::endl;
    std::cout << "========================\n" << std::endl;
    
    for (size_t i = 0; i < triggers.size(); ++i) {
        const auto& trigger = triggers[i];
        if (!trigger) continue;
        
        std::cout << "【触发器 " << (i + 1) << "】" << std::endl;
        
        // 打印基础信息
        std::cout << "  启用状态: " << (trigger->enabled ? "启用" : "禁用") << std::endl;
        std::cout << "  持续时间: " << trigger->duration << " 秒" << std::endl;
        
        // 打印任务参数
        if (!trigger->task_param.task_name.empty()) {
            std::cout << "  任务名称: " << trigger->task_param.task_name << std::endl;
        }
        if (!trigger->task_param.task_source.empty()) {
            std::cout << "  任务来源: " << trigger->task_param.task_source << std::endl;
        }
        if (!trigger->task_param.collect_scene.empty()) {
            std::cout << "  采集场景: " << trigger->task_param.collect_scene << std::endl;
        }
        if (!trigger->task_param.collect_reason.empty()) {
            std::cout << "  采集原因: " << trigger->task_param.collect_reason << std::endl;
        }
        
        // 打印数据源
        if (!trigger->task_param.data_sources.empty()) {
            std::cout << "  数据源 (" << trigger->task_param.data_sources.size() << "个): ";
            for (size_t j = 0; j < trigger->task_param.data_sources.size(); ++j) {
                std::cout << trigger->task_param.data_sources[j];
                if (j < trigger->task_param.data_sources.size() - 1) std::cout << ", ";
            }
            std::cout << std::endl;
        }
        
        // 根据类型打印特定参数
        switch (trigger->type) {
            case TriggerType::TRAFFIC_LIGHT_MISSING_IN_RANGE: {
                auto* param = trigger->as<TrafficLightMissingInRange>();
                std::cout << "  类型: 红绿灯检测范围内缺失" << std::endl;
                std::cout << "  检测范围阈值: " << param->detection_range_threshold << " 米" << std::endl;
                break;
            }
            
            case TriggerType::TRAFFIC_LIGHT_DETECTED_OUT_OF_RANGE: {
                auto* param = trigger->as<TrafficLightDetectedOutOfRange>();
                std::cout << "  类型: 红绿灯检测范围外误检" << std::endl;
                std::cout << "  误检距离阈值: " << param->false_detection_distance_threshold << " 米" << std::endl;
                break;
            }
            
            case TriggerType::TRAFFIC_LIGHT_ILLOGICAL_STATE_CHANGE: {
                auto* param = trigger->as<TrafficLightIllogicalStateChange>();
                std::cout << "  类型: 红绿灯不符常识状态变化" << std::endl;
                std::cout << "  距离范围: [" << param->distance_range_min << ", " << param->distance_range_max << "] 米" << std::endl;
                if (!param->jump_patterns.empty()) {
                    std::cout << "  跳变模式 (" << param->jump_patterns.size() << "种): ";
                    for (size_t j = 0; j < param->jump_patterns.size(); ++j) {
                        std::cout << param->jump_patterns[j];
                        if (j < param->jump_patterns.size() - 1) std::cout << ", ";
                    }
                    std::cout << std::endl;
                }
                break;
            }
            
            case TriggerType::TRAFFIC_LIGHT_VIOLATE_TRAFFIC_FLOW: {
                auto* param = trigger->as<TrafficLightViolateTrafficFlow>();
                std::cout << "  类型: 红绿灯违背交通流" << std::endl;
                std::cout << "  车流平均速度阈值: " << param->traffic_flow_average_speed_threshold << " km/h" << std::endl;
                break;
            }
            
            case TriggerType::OBJECT_DETECTION_INSTABILITY: {
                auto* param = trigger->as<ObjectDetectionInstability>();
                std::cout << "  类型: 目标检测不稳定" << std::endl;
                std::cout << "  检测距离阈值: " << param->detection_distance_threshold << " 米" << std::endl;
                break;
            }
            
            case TriggerType::OBSTACLE_DETECTION_SUDDEN_APPEARANCE: {
                auto* param = trigger->as<ObstacleDetectionSuddenAppearance>();
                std::cout << "  类型: 障碍物突现" << std::endl;
                std::cout << "  障碍物距离阈值: " << param->obstacle_distance_threshold << " 米" << std::endl;
                break;
            }
        }
        
        std::cout << std::endl;  // 空行分隔
    }
    
    std::cout << "========================\n" << std::endl;
}